{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/GroupInfo.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Group, User, UserInfo, Media, GroupRole } from \"@/types/base\";\r\nimport { getLinkIcon, getLinkTitle } from \"@/utils/link-utils\";\r\nimport MediaViewer from \"@/components/media/MediaViewer\";\r\nimport GroupInfoSocketHandler from \"../group/GroupInfoSocketHandler\";\r\nimport {\r\n  X,\r\n  Users,\r\n  UserPlus,\r\n  Settings,\r\n  LogOut,\r\n  Bell,\r\n  Pin,\r\n  FileImage,\r\n  ChevronRight,\r\n  Trash,\r\n  Video,\r\n  ChevronDown,\r\n  ArrowLeft,\r\n  MoreHorizontal,\r\n  UserMinus,\r\n  Shield,\r\n  Ban,\r\n  Link as LinkIcon,\r\n  Pencil,\r\n  RefreshCw,\r\n  QrCode,\r\n  Crown,\r\n} from \"lucide-react\";\r\nimport GroupDialog from \"../group/GroupDialog\";\r\nimport MediaGalleryView from \"./MediaGalleryView\";\r\nimport { <PERSON>roll<PERSON><PERSON> } from \"@/components/ui/scroll-area\";\r\nimport EditGroupNameDialog from \"../group/EditGroupNameDialog\";\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport { getUserDataById, batchGetUserData } from \"@/actions/user.action\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport { useChatStore, type ChatState } from \"@/stores/chatStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\n\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  getRelationship,\r\n  batchGetRelationships,\r\n} from \"@/actions/friend.action\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  deleteGroup,\r\n  leaveGroup,\r\n  removeGroupMember,\r\n  updateMemberRole,\r\n  getGroupById,\r\n} from \"@/actions/group.action\";\r\nimport AddMemberDialog from \"../group/AddMemberDialog\";\r\nimport GroupQRCodeDialog from \"../GroupQRCodeDialog\";\r\n\r\ninterface GroupInfoProps {\r\n  group: Group | null;\r\n  onClose: () => void;\r\n  isOverlay?: boolean;\r\n}\r\n\r\nexport default function GroupInfo({\r\n  group: initialGroup,\r\n  onClose,\r\n  isOverlay = false,\r\n}: GroupInfoProps) {\r\n  // Lấy selectedGroup trực tiếp từ store để đảm bảo luôn có dữ liệu mới nhất\r\n  const selectedGroup = useChatStore((state) => state.selectedGroup);\r\n\r\n  // Sử dụng state để lưu trữ dữ liệu nhóm hiện tại\r\n  // Ưu tiên sử dụng selectedGroup từ store, nếu không có thì dùng initialGroup\r\n  const [group, setGroup] = useState<Group | null>(\r\n    selectedGroup || initialGroup,\r\n  );\r\n\r\n  // Cập nhật group state khi selectedGroup hoặc initialGroup thay đổi\r\n  useEffect(() => {\r\n    // Thêm throttle để tránh cập nhật quá thường xuyên\r\n    if (!window._lastGroupInfoStateUpdateTime) {\r\n      window._lastGroupInfoStateUpdateTime = {};\r\n    }\r\n\r\n    const groupId = selectedGroup?.id || initialGroup?.id;\r\n    if (!groupId) return;\r\n\r\n    const now = Date.now();\r\n    const lastUpdateTime = window._lastGroupInfoStateUpdateTime[groupId] || 0;\r\n    const timeSinceLastUpdate = now - lastUpdateTime;\r\n\r\n    // Nếu đã cập nhật trong vòng 1 giây, bỏ qua\r\n    if (timeSinceLastUpdate < 1000) {\r\n      console.log(\r\n        `[GroupInfo] Skipping state update, last update was ${timeSinceLastUpdate}ms ago`,\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Cập nhật thời gian cập nhật cuối cùng\r\n    window._lastGroupInfoStateUpdateTime[groupId] = now;\r\n\r\n    // Ưu tiên sử dụng selectedGroup từ store\r\n    if (selectedGroup) {\r\n      console.log(\"[GroupInfo] Updating group from selectedGroup in store\");\r\n      console.log(\r\n        \"[GroupInfo] Members count:\",\r\n        selectedGroup.members?.length || 0,\r\n      );\r\n      setGroup(selectedGroup);\r\n    } else if (initialGroup) {\r\n      console.log(\"[GroupInfo] Updating group from initialGroup prop\");\r\n      console.log(\r\n        \"[GroupInfo] Members count:\",\r\n        initialGroup.members?.length || 0,\r\n      );\r\n      setGroup(initialGroup);\r\n    }\r\n  }, [selectedGroup, initialGroup]);\r\n  const [mediaFiles, setMediaFiles] = useState<(Media & { createdAt: Date })[]>(\r\n    [],\r\n  );\r\n  const [documents, setDocuments] = useState<(Media & { createdAt: Date })[]>(\r\n    [],\r\n  );\r\n  const [showMediaGallery, setShowMediaGallery] = useState(false);\r\n  const [showMediaViewer, setShowMediaViewer] = useState(false);\r\n  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);\r\n  const [links, setLinks] = useState<\r\n    { url: string; title: string; timestamp: Date }[]\r\n  >([]);\r\n  const [isLoadingMedia, setIsLoadingMedia] = useState(true);\r\n\r\n  // Reset media gallery and viewer when group changes\r\n  useEffect(() => {\r\n    setShowMediaGallery(false);\r\n    setShowMediaViewer(false);\r\n  }, [group?.id]);\r\n  const [showMembersList, setShowMembersList] = useState(false);\r\n  const [memberDetails, setMemberDetails] = useState<{\r\n    [key: string]: User & { userInfo: UserInfo };\r\n  }>({});\r\n  const [adderDetails, setAdderDetails] = useState<{ [key: string]: User }>({});\r\n  const [relationships, setRelationships] = useState<{ [key: string]: string }>(\r\n    {},\r\n  );\r\n  const [isSendingRequest] = useState<{ [key: string]: boolean }>({});\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const [showLeaveDialog, setShowLeaveDialog] = useState(false);\r\n  const [showTransferLeadershipDialog, setShowTransferLeadershipDialog] =\r\n    useState(false);\r\n  const [showConfirmTransferDialog, setShowConfirmTransferDialog] =\r\n    useState(false);\r\n  const [newLeaderId, setNewLeaderId] = useState<string | null>(null);\r\n  const [showAddMemberDialog, setShowAddMemberDialog] = useState(false);\r\n  const [isProcessing, setIsProcessing] = useState(false);\r\n  const [currentUserRole, setCurrentUserRole] = useState<GroupRole | null>(\r\n    null,\r\n  );\r\n  const [showGroupDialog, setShowGroupDialog] = useState(false);\r\n  const [showKickDialog, setShowKickDialog] = useState(false);\r\n  const [showPromoteDialog, setShowPromoteDialog] = useState(false);\r\n  const [showDemoteDialog, setShowDemoteDialog] = useState(false);\r\n  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);\r\n  const [selectedMember, setSelectedMember] = useState<User | null>(null);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const [showFriendRequestForm, setShowFriendRequestForm] = useState(false);\r\n  const [openDropdownMemberId, setOpenDropdownMemberId] = useState<\r\n    string | null\r\n  >(null);\r\n  const [showEditNameDialog, setShowEditNameDialog] = useState(false);\r\n  const [activeGalleryTab, setActiveGalleryTab] = useState<\r\n    \"media\" | \"files\" | \"links\"\r\n  >(\"media\");\r\n  const [showGroupQRDialog, setShowGroupQRDialog] = useState(false);\r\n\r\n  const messages = useChatStore((state) => state.messages);\r\n  const currentUser = useAuthStore((state) => state.user);\r\n  // const groupSocket = useGroupSocket();\r\n  // Removed forceUpdate state as it was causing infinite loops\r\n\r\n  // Hàm cập nhật danh sách thành viên (sử dụng useCallback để tránh tạo hàm mới mỗi khi render)\r\n  const updateMembersList = useCallback(\r\n    async (forceRefresh = false) => {\r\n      const groupId = group?.id || selectedGroup?.id || initialGroup?.id;\r\n      if (!groupId) return false;\r\n\r\n      // Thêm throttle để tránh gọi API quá thường xuyên\r\n      if (!window._lastGroupInfoApiCallTime) {\r\n        window._lastGroupInfoApiCallTime = {};\r\n      }\r\n\r\n      const now = Date.now();\r\n      const lastCallTime = window._lastGroupInfoApiCallTime[groupId] || 0;\r\n      const timeSinceLastCall = now - lastCallTime;\r\n\r\n      // Nếu đã gọi API trong vòng 2 giây và không phải là force refresh, bỏ qua\r\n      if (timeSinceLastCall < 2000 && !forceRefresh) {\r\n        console.log(\r\n          `[GroupInfo] Skipping API call, last call was ${timeSinceLastCall}ms ago`,\r\n        );\r\n        return true;\r\n      }\r\n\r\n      // Cập nhật thời gian gọi API\r\n      window._lastGroupInfoApiCallTime[groupId] = now;\r\n\r\n      console.log(\r\n        \"[GroupInfo] Updating members list for group\",\r\n        groupId,\r\n        \"forceRefresh:\",\r\n        forceRefresh,\r\n      );\r\n\r\n      // Check if we have a valid cache for this group\r\n      const chatStore = useChatStore.getState() as ChatState & {\r\n        setShouldFetchGroupData?: (shouldFetch: boolean) => void;\r\n        clearGroupCache?: (groupId: string) => void;\r\n      };\r\n      const cachedData = chatStore.groupCache\r\n        ? chatStore.groupCache[groupId]\r\n        : undefined;\r\n      const currentTime = new Date();\r\n      const isCacheValid =\r\n        cachedData &&\r\n        !forceRefresh && // Always refresh if forceRefresh is true\r\n        currentTime.getTime() - cachedData.lastFetched.getTime() < 30 * 1000; // 30 seconds cache\r\n\r\n      if (isCacheValid) {\r\n        console.log(`[GroupInfo] Using cached group data for ${groupId}`);\r\n\r\n        // Update the group state with cached data\r\n        setGroup(cachedData.group);\r\n\r\n        // Don't update forceUpdate here to prevent infinite loops\r\n        return true;\r\n      }\r\n\r\n      try {\r\n        // Lấy dữ liệu nhóm mới trực tiếp từ API để đảm bảo dữ liệu mới nhất\r\n        console.log(\"[GroupInfo] Fetching fresh group data from API\");\r\n        const result = await getGroupById(groupId);\r\n\r\n        if (result.success && result.group) {\r\n          console.log(\r\n            \"[GroupInfo] Successfully fetched fresh group data from API\",\r\n          );\r\n          console.log(\r\n            \"[GroupInfo] Members count:\",\r\n            result.group.members?.length || 0,\r\n          );\r\n          console.log(\r\n            \"[GroupInfo] Current members count:\",\r\n            group?.members?.length || 0,\r\n          );\r\n\r\n          // Kiểm tra xem số lượng thành viên có thay đổi không\r\n          const membersChanged =\r\n            group?.members?.length !== result.group.members?.length;\r\n          console.log(\"[GroupInfo] Members changed:\", membersChanged);\r\n\r\n          // Cập nhật group state với dữ liệu mới từ API\r\n          setGroup(result.group);\r\n\r\n          // Cập nhật selectedGroup trong store\r\n          chatStore.setSelectedGroup(result.group);\r\n\r\n          // Update the cache\r\n          if (chatStore.groupCache) {\r\n            // Fallback for direct cache manipulation\r\n            chatStore.groupCache[groupId] = {\r\n              group: result.group,\r\n              lastFetched: new Date(),\r\n            };\r\n          }\r\n\r\n          // Cập nhật conversations store để đảm bảo UI được cập nhật đồng bộ\r\n          useConversationsStore.getState().updateConversation(groupId, {\r\n            group: {\r\n              id: result.group.id,\r\n              name: result.group.name,\r\n              avatarUrl: result.group.avatarUrl,\r\n              createdAt: result.group.createdAt,\r\n              memberUsers: result.group.memberUsers,\r\n            },\r\n          });\r\n\r\n          // Nếu số lượng thành viên thay đổi, hiển thị thông báo\r\n          if (membersChanged && !forceRefresh) {\r\n            if (\r\n              (group?.members?.length || 0) <\r\n              (result.group.members?.length || 0)\r\n            ) {\r\n              toast.info(\"Thành viên mới đã được thêm vào nhóm\");\r\n            } else if (\r\n              (group?.members?.length || 0) >\r\n              (result.group.members?.length || 0)\r\n            ) {\r\n              toast.info(\"Một thành viên đã bị xóa khỏi nhóm\");\r\n            }\r\n          }\r\n\r\n          return true;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"[GroupInfo] Error fetching group data:\", error);\r\n      }\r\n\r\n      // Nếu không thể lấy dữ liệu từ API, thử dùng dữ liệu từ store\r\n      const storeSelectedGroup = chatStore.selectedGroup;\r\n      if (storeSelectedGroup && storeSelectedGroup.id === groupId) {\r\n        console.log(\"[GroupInfo] Falling back to store data\");\r\n        setGroup(storeSelectedGroup);\r\n        // Don't update forceUpdate here to prevent infinite loops\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    },\r\n    [group?.id, group?.members?.length, selectedGroup?.id, initialGroup?.id],\r\n  );\r\n\r\n  // Hàm làm mới dữ liệu nhóm\r\n  const handleRefreshGroup = async () => {\r\n    toast.info(\"Đang làm mới dữ liệu nhóm...\");\r\n\r\n    try {\r\n      // Force refresh by passing true to updateMembersList\r\n      const success = await updateMembersList(true);\r\n\r\n      if (success) {\r\n        toast.success(\"Làm mới dữ liệu nhóm thành công\");\r\n        return;\r\n      }\r\n\r\n      // Nếu không thể cập nhật qua updateMembersList, thử sử dụng refreshSelectedGroup\r\n      console.log(\"[GroupInfo] Trying refreshSelectedGroup as fallback\");\r\n\r\n      // Clear the cache to force a refresh\r\n      const groupId = group?.id || selectedGroup?.id || initialGroup?.id;\r\n      if (groupId) {\r\n        const chatStore = useChatStore.getState();\r\n        // Clear the cache entry for this group\r\n        if (chatStore.groupCache && chatStore.groupCache[groupId]) {\r\n          delete chatStore.groupCache[groupId];\r\n        }\r\n      }\r\n\r\n      await useChatStore.getState().refreshSelectedGroup();\r\n\r\n      // Kiểm tra xem selectedGroup đã được cập nhật chưa\r\n      const updatedSelectedGroup = useChatStore.getState().selectedGroup;\r\n\r\n      if (updatedSelectedGroup && updatedSelectedGroup.id === groupId) {\r\n        console.log(\"[GroupInfo] Successfully refreshed group data via store\");\r\n        console.log(\r\n          \"[GroupInfo] New members count:\",\r\n          updatedSelectedGroup.members?.length || 0,\r\n        );\r\n\r\n        // Cập nhật group state\r\n        setGroup(updatedSelectedGroup);\r\n\r\n        // No need to update forceUpdate to prevent infinite loops\r\n\r\n        // Cập nhật conversations store để đảm bảo UI được cập nhật đồng bộ\r\n        if (groupId && updatedSelectedGroup) {\r\n          useConversationsStore.getState().updateConversation(groupId, {\r\n            group: {\r\n              id: updatedSelectedGroup.id,\r\n              name: updatedSelectedGroup.name,\r\n              avatarUrl: updatedSelectedGroup.avatarUrl,\r\n              createdAt: updatedSelectedGroup.createdAt,\r\n              memberUsers: updatedSelectedGroup.memberUsers,\r\n            },\r\n          });\r\n        }\r\n\r\n        toast.success(\"Làm mới dữ liệu nhóm thành công\");\r\n        return;\r\n      }\r\n\r\n      // Nếu không thể lấy dữ liệu từ API, thử dùng triggerGroupsReload\r\n      if (typeof window !== \"undefined\" && window.triggerGroupsReload) {\r\n        console.log(\"[GroupInfo] Triggering global group reload event\");\r\n        window.triggerGroupsReload();\r\n        // No need to update forceUpdate to prevent infinite loops\r\n      } else {\r\n        toast.error(\"Không thể làm mới dữ liệu nhóm\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error refreshing group data:\", error);\r\n      toast.error(\"Không thể làm mới dữ liệu nhóm\");\r\n    }\r\n  };\r\n\r\n  // Sử dụng cách tiếp cận giống với ChatHeader để lấy thông tin nhóm từ conversationsStore\r\n  // Lấy danh sách cuộc trò chuyện từ conversationsStore\r\n  const conversations = useConversationsStore((state) => state.conversations);\r\n\r\n  // Tìm thông tin nhóm từ conversationsStore\r\n  const groupConversation = useMemo(() => {\r\n    if (!initialGroup?.id) return null;\r\n    return conversations.find(\r\n      (conv) => conv.type === \"GROUP\" && conv.group?.id === initialGroup.id,\r\n    );\r\n  }, [conversations, initialGroup?.id]);\r\n\r\n  // Tính toán số lượng thành viên từ conversationsStore\r\n  const memberCount = useMemo(() => {\r\n    // Ưu tiên sử dụng thông tin từ conversationsStore\r\n    if (groupConversation?.group?.memberUsers) {\r\n      return groupConversation.group.memberUsers.length;\r\n    }\r\n    // Nếu không có, sử dụng thông tin từ group state\r\n    return group?.members?.length || 0;\r\n  }, [groupConversation?.group?.memberUsers, group?.members]);\r\n\r\n  // Lấy thông tin chi tiết của các thành viên và vai trò của người dùng hiện tại\r\n  useEffect(() => {\r\n    if (group?.id && group.members) {\r\n      const fetchMemberDetails = async () => {\r\n        const newMemberDetails: {\r\n          [key: string]: User & { userInfo: UserInfo };\r\n        } = {};\r\n        const newAdderDetails: { [key: string]: User } = {};\r\n        const newRelationships: { [key: string]: string } = {};\r\n\r\n        try {\r\n          // Collect all user IDs that need to be fetched\r\n          const memberIds: string[] = [];\r\n          const adderIds: string[] = [];\r\n          const relationshipIds: string[] = [];\r\n\r\n          // Prepare lists of IDs to fetch\r\n          for (const member of group.members) {\r\n            // Check if we need to fetch user data\r\n            if (!member.user?.userInfo) {\r\n              memberIds.push(member.userId);\r\n            } else {\r\n              // If we already have the data, store it\r\n              newMemberDetails[member.userId] = member.user as User & {\r\n                userInfo: UserInfo;\r\n              };\r\n            }\r\n\r\n            // Check if we need to fetch adder data\r\n            if (\r\n              member.addedBy &&\r\n              typeof member.addedBy === \"object\" &&\r\n              \"id\" in member.addedBy &&\r\n              \"fullName\" in member.addedBy\r\n            ) {\r\n              // Create a simple User object with the addedBy information\r\n              const adderInfo = member.addedBy as unknown as {\r\n                id: string;\r\n                fullName: string;\r\n              };\r\n              newAdderDetails[member.userId] = {\r\n                id: adderInfo.id,\r\n                userInfo: {\r\n                  id: adderInfo.id,\r\n                  fullName: adderInfo.fullName,\r\n                  blockStrangers: false,\r\n                  createdAt: new Date(),\r\n                  updatedAt: new Date(),\r\n                  userAuth: { id: adderInfo.id } as User,\r\n                },\r\n              } as unknown as User;\r\n            } else if (\r\n              member.addedById &&\r\n              member.addedById !== currentUser?.id &&\r\n              !member.addedBy\r\n            ) {\r\n              adderIds.push(member.addedById);\r\n            } else if (member.addedBy && \"userInfo\" in member.addedBy) {\r\n              newAdderDetails[member.userId] = member.addedBy as User;\r\n            }\r\n\r\n            // Check if we need to fetch relationship data\r\n            if (member.userId !== currentUser?.id) {\r\n              relationshipIds.push(member.userId);\r\n            }\r\n\r\n            // Set current user role\r\n            if (currentUser && member.userId === currentUser.id) {\r\n              setCurrentUserRole(member.role);\r\n            }\r\n          }\r\n\r\n          // Batch fetch user data\r\n          if (memberIds.length > 0) {\r\n            console.log(`Batch fetching ${memberIds.length} member details`);\r\n            const userResult = await batchGetUserData(memberIds);\r\n            if (userResult.success && userResult.users) {\r\n              userResult.users.forEach((user) => {\r\n                newMemberDetails[user.id] = user as User & {\r\n                  userInfo: UserInfo;\r\n                };\r\n              });\r\n            }\r\n          }\r\n\r\n          // Batch fetch adder data\r\n          if (adderIds.length > 0) {\r\n            console.log(`Batch fetching ${adderIds.length} adder details`);\r\n            const adderResult = await batchGetUserData(adderIds);\r\n            if (adderResult.success && adderResult.users) {\r\n              // Match adders to members\r\n              for (const member of group.members) {\r\n                if (member.addedById) {\r\n                  const adder = adderResult.users.find(\r\n                    (u) => u.id === member.addedById,\r\n                  );\r\n                  if (adder) {\r\n                    newAdderDetails[member.userId] = adder;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          // Batch fetch relationship data\r\n          if (relationshipIds.length > 0) {\r\n            console.log(\r\n              `Batch fetching ${relationshipIds.length} relationships`,\r\n            );\r\n            const accessToken =\r\n              useAuthStore.getState().accessToken || undefined;\r\n            const relationshipResult = await batchGetRelationships(\r\n              relationshipIds,\r\n              accessToken,\r\n            );\r\n\r\n            if (\r\n              relationshipResult.success &&\r\n              relationshipResult.relationships\r\n            ) {\r\n              // Process relationships\r\n              Object.entries(relationshipResult.relationships).forEach(\r\n                ([userId, data]) => {\r\n                  // Normalize relationship status\r\n                  const status = data.status || \"NONE\";\r\n\r\n                  // Standardize relationship values\r\n                  if (status === \"ACCEPTED\" || status === \"FRIEND\") {\r\n                    newRelationships[userId] = \"ACCEPTED\";\r\n                  } else if (status === \"PENDING_SENT\") {\r\n                    newRelationships[userId] = \"PENDING_SENT\";\r\n                  } else if (status === \"PENDING_RECEIVED\") {\r\n                    newRelationships[userId] = \"PENDING_RECEIVED\";\r\n                  } else {\r\n                    newRelationships[userId] = status;\r\n                  }\r\n\r\n                  console.log(\r\n                    `Normalized relationship with ${userId}:`,\r\n                    newRelationships[userId],\r\n                  );\r\n                },\r\n              );\r\n            }\r\n          }\r\n\r\n          // Set default relationship status for any members without data\r\n          for (const member of group.members) {\r\n            if (\r\n              member.userId !== currentUser?.id &&\r\n              !newRelationships[member.userId]\r\n            ) {\r\n              newRelationships[member.userId] = \"NONE\";\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching member details:\", error);\r\n        }\r\n\r\n        // Update state with all the data we collected\r\n        setMemberDetails(newMemberDetails);\r\n        setAdderDetails(newAdderDetails);\r\n        setRelationships(newRelationships);\r\n      };\r\n\r\n      fetchMemberDetails();\r\n    }\r\n  }, [group?.id, group?.members, currentUser]); // Removed forceUpdate from dependencies\r\n\r\n  // Lấy media từ tin nhắn\r\n  useEffect(() => {\r\n    if (group?.id) {\r\n      setIsLoadingMedia(true);\r\n\r\n      // Lọc media từ tin nhắn hiện có\r\n      const extractMediaFromMessages = () => {\r\n        try {\r\n          const imageAndVideoFiles: (Media & {\r\n            createdAt: Date;\r\n            sender?: unknown;\r\n            senderId?: string;\r\n          })[] = [];\r\n          const documentFiles: (Media & {\r\n            createdAt: Date;\r\n            sender?: unknown;\r\n            senderId?: string;\r\n          })[] = [];\r\n          const extractedLinks: {\r\n            url: string;\r\n            title: string;\r\n            timestamp: Date;\r\n          }[] = [];\r\n\r\n          if (!Array.isArray(messages)) {\r\n            console.warn(\"[GroupInfo] Messages is not an array:\", messages);\r\n            return;\r\n          }\r\n\r\n          messages.forEach((message) => {\r\n            try {\r\n              // Skip invalid messages\r\n              if (!message || typeof message !== \"object\") {\r\n                console.warn(\"[GroupInfo] Invalid message object:\", message);\r\n                return;\r\n              }\r\n\r\n              // Skip recalled messages\r\n              if (message.recalled) return;\r\n\r\n              // Skip messages without content\r\n              if (!message.content) {\r\n                console.warn(\"[GroupInfo] Message without content:\", message);\r\n                return;\r\n              }\r\n\r\n              // Process media\r\n              const media = message.content.media;\r\n              if (Array.isArray(media) && media.length > 0) {\r\n                media.forEach((mediaItem) => {\r\n                  if (!mediaItem?.metadata?.extension) {\r\n                    console.warn(\r\n                      \"[GroupInfo] Media item missing metadata or extension:\",\r\n                      mediaItem,\r\n                    );\r\n                    return;\r\n                  }\r\n\r\n                  const extension = mediaItem.metadata.extension.toLowerCase();\r\n                  const mediaWithDate = {\r\n                    ...mediaItem,\r\n                    createdAt: new Date(message.createdAt || Date.now()),\r\n                    sender: message.sender,\r\n                    senderId: message.senderId,\r\n                  };\r\n\r\n                  if (\r\n                    [\r\n                      \"jpg\",\r\n                      \"jpeg\",\r\n                      \"png\",\r\n                      \"gif\",\r\n                      \"webp\",\r\n                      \"mp4\",\r\n                      \"webm\",\r\n                      \"mov\",\r\n                    ].includes(extension)\r\n                  ) {\r\n                    imageAndVideoFiles.push(mediaWithDate);\r\n                  } else {\r\n                    documentFiles.push(mediaWithDate);\r\n                  }\r\n                });\r\n              }\r\n\r\n              // Process links in text\r\n              const text = message.content.text;\r\n              if (typeof text === \"string\" && text.length > 0) {\r\n                const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\r\n                const matches = text.match(urlRegex);\r\n                if (matches) {\r\n                  matches.forEach((url) => {\r\n                    try {\r\n                      // Get domain for display\r\n                      const domain = url\r\n                        .replace(/^https?:\\/\\//, \"\")\r\n                        .split(\"/\")[0];\r\n                      // Use the utility function to get a better title\r\n                      const title = getLinkTitle(domain, url);\r\n                      extractedLinks.push({\r\n                        url,\r\n                        title,\r\n                        timestamp: new Date(message.createdAt || Date.now()),\r\n                      });\r\n                    } catch (error) {\r\n                      console.warn(\r\n                        \"[GroupInfo] Error processing URL:\",\r\n                        url,\r\n                        error,\r\n                      );\r\n                    }\r\n                  });\r\n                }\r\n              }\r\n            } catch (error) {\r\n              console.warn(\r\n                \"[GroupInfo] Error processing message:\",\r\n                message,\r\n                error,\r\n              );\r\n            }\r\n          });\r\n\r\n          // Sort media from newest to oldest\r\n          imageAndVideoFiles.sort(\r\n            (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\r\n          );\r\n          documentFiles.sort(\r\n            (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\r\n          );\r\n          extractedLinks.sort(\r\n            (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),\r\n          );\r\n\r\n          setMediaFiles(imageAndVideoFiles.slice(0, 20)); // Limit to 20 files\r\n          setDocuments(documentFiles.slice(0, 10)); // Limit to 10 files\r\n          setLinks(extractedLinks.slice(0, 10)); // Limit to 10 links\r\n        } catch (error) {\r\n          console.error(\r\n            \"[GroupInfo] Error in extractMediaFromMessages:\",\r\n            error,\r\n          );\r\n        } finally {\r\n          setIsLoadingMedia(false);\r\n        }\r\n      };\r\n\r\n      extractMediaFromMessages();\r\n    }\r\n  }, [group?.id, messages]);\r\n\r\n  // Kiểm tra nếu không có dữ liệu nhóm\r\n  useEffect(() => {\r\n    if (!initialGroup && !group) {\r\n      console.log(\"[GroupInfo] No group data available\");\r\n      // Đóng GroupInfo nếu không có dữ liệu nhóm\r\n      if (onClose) {\r\n        onClose();\r\n      }\r\n    }\r\n  }, [initialGroup, group, onClose]);\r\n\r\n  if (!group) {\r\n    return (\r\n      <div className=\"h-full flex items-center justify-center\">\r\n        <div className=\"text-center p-4\">\r\n          <div className=\"animate-spin h-8 w-8 border-2 border-blue-500 rounded-full border-t-transparent mx-auto mb-4\"></div>\r\n          <p>Đang tải thông tin nhóm...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const handleMemberClick = async (memberId: string) => {\r\n    // Kiểm tra xem đã có thông tin thành viên trong memberDetails chưa\r\n    if (memberDetails[memberId]) {\r\n      setSelectedMember(memberDetails[memberId]);\r\n      setShowProfileDialog(true);\r\n      return;\r\n    }\r\n\r\n    // Nếu member.user đã có đầy đủ thông tin, sử dụng luôn mà không cần gọi API\r\n    const memberWithData = group?.members?.find(\r\n      (m) => m.userId === memberId && m.user?.userInfo,\r\n    );\r\n    if (memberWithData?.user) {\r\n      setSelectedMember(memberWithData.user as User & { userInfo: UserInfo });\r\n      setShowProfileDialog(true);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Nếu không có sẵn thông tin, gọi API để lấy\r\n      const result = await getUserDataById(memberId);\r\n      if (result.success && result.user) {\r\n        setSelectedMember(result.user as User & { userInfo: UserInfo });\r\n        setShowProfileDialog(true);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching member data:\", error);\r\n      // Không hiển thị dialog nếu không thể lấy thông tin chi tiết\r\n      console.log(\r\n        \"Không thể lấy thông tin thành viên, bỏ qua việc mở ProfileDialog\",\r\n      );\r\n    }\r\n  };\r\n\r\n  if (showMediaGallery) {\r\n    return (\r\n      <MediaGalleryView\r\n        mediaFiles={mediaFiles}\r\n        documents={documents}\r\n        links={links}\r\n        initialTab={activeGalleryTab}\r\n        onClose={() => setShowMediaGallery(false)}\r\n      />\r\n    );\r\n  }\r\n\r\n  // Handle send friend request\r\n  const handleSendFriendRequest = (userId: string) => {\r\n    const memberData = memberDetails[userId];\r\n    if (memberData) {\r\n      setSelectedMember(memberData);\r\n      setShowFriendRequestForm(true);\r\n      setShowProfileDialog(true);\r\n      setOpenDropdownMemberId(null); // Close dropdown after action\r\n    }\r\n  };\r\n\r\n  if (showMembersList) {\r\n    return (\r\n      <div\r\n        className={`h-full flex flex-col bg-white ${!isOverlay ? \"border-l\" : \"\"}`}\r\n      >\r\n        <div className=\"p-4 flex items-center justify-between border-b\">\r\n          <div className=\"flex items-center\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"mr-2\"\r\n              onClick={() => setShowMembersList(false)}\r\n            >\r\n              <ArrowLeft className=\"h-5 w-5\" />\r\n            </Button>\r\n            <h2 className=\"font-semibold\">Thành viên</h2>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"p-4 border-b\">\r\n          <Button\r\n            className=\"w-full flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-black\"\r\n            onClick={() => {\r\n              setShowMembersList(false);\r\n              setShowAddMemberDialog(true);\r\n            }}\r\n          >\r\n            <UserPlus className=\"h-4 w-4\" />\r\n            <span>Thêm thành viên</span>\r\n          </Button>\r\n        </div>\r\n\r\n        <div className=\"p-4 flex justify-between items-center\">\r\n          <span className=\"text-sm\">Danh sách thành viên ({memberCount})</span>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={handleRefreshGroup}\r\n            title=\"Làm mới danh sách thành viên\"\r\n          >\r\n            <RefreshCw className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          {/* Ưu tiên sử dụng memberUsers từ conversationsStore nếu có */}\r\n          {groupConversation?.group?.memberUsers\r\n            ? // Hiển thị danh sách thành viên từ conversationsStore\r\n              groupConversation.group.memberUsers.map((member) => {\r\n                const initials = member.fullName\r\n                  ? member.fullName.slice(0, 2).toUpperCase()\r\n                  : \"??\";\r\n\r\n                return (\r\n                  <div\r\n                    key={`${member.id}`}\r\n                    className=\"flex items-center p-4 hover:bg-gray-100 justify-between\"\r\n                  >\r\n                    <div\r\n                      className=\"flex items-center cursor-pointer\"\r\n                      onClick={() => handleMemberClick(member.id)}\r\n                    >\r\n                      <Avatar className=\"h-10 w-10 mr-3\">\r\n                        <AvatarImage\r\n                          src={member.profilePictureUrl || undefined}\r\n                          className=\"object-cover\"\r\n                        />\r\n                        <AvatarFallback className=\"bg-gray-200 text-gray-600\">\r\n                          {initials}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium\">\r\n                          {member.fullName || \"Thành viên\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          {member.role === \"LEADER\"\r\n                            ? \"Trưởng nhóm\"\r\n                            : member.role === \"CO_LEADER\"\r\n                              ? \"Phó nhóm\"\r\n                              : \"\"}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                      {/* Show pending status */}\r\n                      {member.id !== currentUser?.id &&\r\n                        relationships[member.id] === \"PENDING_SENT\" && (\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            disabled\r\n                            title=\"Đã gửi lời mời kết bạn\"\r\n                          >\r\n                            <LinkIcon className=\"h-4 w-4 text-gray-400\" />\r\n                          </Button>\r\n                        )}\r\n\r\n                      {/* Hiển thị menu tùy chọn cho thành viên (không hiển thị cho chính mình) */}\r\n                      {member.id !== currentUser?.id && (\r\n                        <DropdownMenu\r\n                          open={openDropdownMemberId === member.id}\r\n                          onOpenChange={(open) => {\r\n                            if (open) {\r\n                              setOpenDropdownMemberId(member.id);\r\n                            } else if (openDropdownMemberId === member.id) {\r\n                              setOpenDropdownMemberId(null);\r\n                            }\r\n                          }}\r\n                        >\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"icon\">\r\n                              <MoreHorizontal className=\"h-5 w-5\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent\r\n                            align=\"end\"\r\n                            onEscapeKeyDown={() =>\r\n                              setOpenDropdownMemberId(null)\r\n                            }\r\n                          >\r\n                            {/* Add friend option if not already friends */}\r\n                            {relationships[member.id] === \"NONE\" && (\r\n                              <DropdownMenuItem\r\n                                onClick={() =>\r\n                                  handleSendFriendRequest(member.id)\r\n                                }\r\n                                disabled={isSendingRequest[member.id]}\r\n                              >\r\n                                {isSendingRequest[member.id] ? (\r\n                                  <>\r\n                                    <div className=\"h-4 w-4 mr-2 rounded-full border-2 border-gray-600 border-t-transparent animate-spin\"></div>\r\n                                    Đang gửi...\r\n                                  </>\r\n                                ) : (\r\n                                  <>\r\n                                    <UserPlus className=\"h-4 w-4 mr-2 text-blue-500\" />\r\n                                    Kết bạn\r\n                                  </>\r\n                                )}\r\n                              </DropdownMenuItem>\r\n                            )}\r\n\r\n                            {/* Leader/Co-leader management options */}\r\n                            {(currentUserRole === \"LEADER\" ||\r\n                              (currentUserRole === \"CO_LEADER\" &&\r\n                                member.role === \"MEMBER\")) && (\r\n                              <>\r\n                                {currentUserRole === \"LEADER\" &&\r\n                                  member.role === \"MEMBER\" && (\r\n                                    <DropdownMenuItem\r\n                                      onClick={() =>\r\n                                        handlePromoteMember(member.id)\r\n                                      }\r\n                                    >\r\n                                      <Shield className=\"h-4 w-4 mr-2\" />\r\n                                      Thăng phó nhóm\r\n                                    </DropdownMenuItem>\r\n                                  )}\r\n                                {currentUserRole === \"LEADER\" &&\r\n                                  member.role === \"CO_LEADER\" && (\r\n                                    <DropdownMenuItem\r\n                                      onClick={() =>\r\n                                        handleDemoteMember(member.id)\r\n                                      }\r\n                                    >\r\n                                      <UserMinus className=\"h-4 w-4 mr-2\" />\r\n                                      Hạ xuống thành viên\r\n                                    </DropdownMenuItem>\r\n                                  )}\r\n                                {currentUserRole === \"LEADER\" &&\r\n                                  (member.role === \"MEMBER\" ||\r\n                                    member.role === \"CO_LEADER\") && (\r\n                                    <DropdownMenuItem\r\n                                      onClick={() =>\r\n                                        handleSelectNewLeader(member.id)\r\n                                      }\r\n                                      className=\"text-orange-500 focus:text-orange-500\"\r\n                                    >\r\n                                      <Crown className=\"h-4 w-4 mr-2\" />\r\n                                      Nhượng quyền trưởng nhóm\r\n                                    </DropdownMenuItem>\r\n                                  )}\r\n                                <DropdownMenuSeparator />\r\n                                <DropdownMenuItem\r\n                                  onClick={() => handleKickMember(member.id)}\r\n                                  className=\"text-red-500 focus:text-red-500\"\r\n                                >\r\n                                  <Ban className=\"h-4 w-4 mr-2\" />\r\n                                  Xóa khỏi nhóm\r\n                                </DropdownMenuItem>\r\n                              </>\r\n                            )}\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })\r\n            : // Fallback sử dụng group.members nếu không có dữ liệu từ conversationsStore\r\n              group.members?.map((member) => {\r\n                // Key bao gồm forceUpdate để đảm bảo danh sách được cập nhật khi có thay đổi\r\n                const memberData = memberDetails[member.userId];\r\n                const initials = memberData?.userInfo?.fullName\r\n                  ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                  : \"??\";\r\n\r\n                return (\r\n                  <div\r\n                    key={`${member.userId}`}\r\n                    className=\"flex items-center p-4 hover:bg-gray-100 justify-between\"\r\n                  >\r\n                    <div\r\n                      className=\"flex items-center cursor-pointer\"\r\n                      onClick={() => handleMemberClick(member.userId)}\r\n                    >\r\n                      <Avatar className=\"h-10 w-10 mr-3\">\r\n                        <AvatarImage\r\n                          src={\r\n                            memberData?.userInfo?.profilePictureUrl || undefined\r\n                          }\r\n                          className=\"object-cover\"\r\n                        />\r\n                        <AvatarFallback className=\"bg-gray-200 text-gray-600\">\r\n                          {initials}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div>\r\n                        <p className=\"font-medium\">\r\n                          {memberData?.userInfo?.fullName || \"Thành viên\"}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          {member.role === \"LEADER\"\r\n                            ? \"Trưởng nhóm\"\r\n                            : member.role === \"CO_LEADER\"\r\n                              ? \"Phó nhóm\"\r\n                              : \"\"}\r\n                        </p>\r\n                        {/* Hiển thị thông tin người thêm */}\r\n                        {member.userId !== currentUser?.id && (\r\n                          <p className=\"text-xs text-gray-500\">\r\n                            {member.addedBy && \"fullName\" in member.addedBy\r\n                              ? `Thêm bởi ${(member.addedBy as unknown as { fullName: string }).fullName}`\r\n                              : adderDetails[member.userId]?.userInfo?.fullName\r\n                                ? `Thêm bởi ${adderDetails[member.userId]?.userInfo?.fullName}`\r\n                                : \"\"}\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                      {/* Show pending status */}\r\n                      {member.userId !== currentUser?.id &&\r\n                        relationships[member.userId] === \"PENDING_SENT\" && (\r\n                          <Button\r\n                            variant=\"ghost\"\r\n                            size=\"icon\"\r\n                            disabled\r\n                            title=\"Đã gửi lời mời kết bạn\"\r\n                          >\r\n                            <LinkIcon className=\"h-4 w-4 text-gray-400\" />\r\n                          </Button>\r\n                        )}\r\n\r\n                      {/* Hiển thị menu tùy chọn cho thành viên (không hiển thị cho chính mình) */}\r\n                      {member.userId !== currentUser?.id && (\r\n                        <DropdownMenu\r\n                          open={openDropdownMemberId === member.userId}\r\n                          onOpenChange={(open) => {\r\n                            if (open) {\r\n                              setOpenDropdownMemberId(member.userId);\r\n                            } else if (openDropdownMemberId === member.userId) {\r\n                              setOpenDropdownMemberId(null);\r\n                            }\r\n                          }}\r\n                        >\r\n                          <DropdownMenuTrigger asChild>\r\n                            <Button variant=\"ghost\" size=\"icon\">\r\n                              <MoreHorizontal className=\"h-5 w-5\" />\r\n                            </Button>\r\n                          </DropdownMenuTrigger>\r\n                          <DropdownMenuContent\r\n                            align=\"end\"\r\n                            onEscapeKeyDown={() =>\r\n                              setOpenDropdownMemberId(null)\r\n                            }\r\n                          >\r\n                            {/* Add friend option if not already friends */}\r\n                            {relationships[member.userId] === \"NONE\" && (\r\n                              <DropdownMenuItem\r\n                                onClick={() =>\r\n                                  handleSendFriendRequest(member.userId)\r\n                                }\r\n                                disabled={isSendingRequest[member.userId]}\r\n                              >\r\n                                {isSendingRequest[member.userId] ? (\r\n                                  <>\r\n                                    <div className=\"h-4 w-4 mr-2 rounded-full border-2 border-gray-600 border-t-transparent animate-spin\"></div>\r\n                                    Đang gửi...\r\n                                  </>\r\n                                ) : (\r\n                                  <>\r\n                                    <UserPlus className=\"h-4 w-4 mr-2 text-blue-500\" />\r\n                                    Kết bạn\r\n                                  </>\r\n                                )}\r\n                              </DropdownMenuItem>\r\n                            )}\r\n\r\n                            {/* Leader/Co-leader management options */}\r\n                            {(currentUserRole === \"LEADER\" ||\r\n                              (currentUserRole === \"CO_LEADER\" &&\r\n                                member.role === \"MEMBER\")) && (\r\n                              <>\r\n                                {currentUserRole === \"LEADER\" &&\r\n                                  member.role === \"MEMBER\" && (\r\n                                    <DropdownMenuItem\r\n                                      onClick={() =>\r\n                                        handlePromoteMember(member.userId)\r\n                                      }\r\n                                    >\r\n                                      <Shield className=\"h-4 w-4 mr-2\" />\r\n                                      Thăng phó nhóm\r\n                                    </DropdownMenuItem>\r\n                                  )}\r\n                                {currentUserRole === \"LEADER\" &&\r\n                                  member.role === \"CO_LEADER\" && (\r\n                                    <DropdownMenuItem\r\n                                      onClick={() =>\r\n                                        handleDemoteMember(member.userId)\r\n                                      }\r\n                                    >\r\n                                      <UserMinus className=\"h-4 w-4 mr-2\" />\r\n                                      Hạ xuống thành viên\r\n                                    </DropdownMenuItem>\r\n                                  )}\r\n                                {currentUserRole === \"LEADER\" &&\r\n                                  (member.role === \"MEMBER\" ||\r\n                                    member.role === \"CO_LEADER\") && (\r\n                                    <DropdownMenuItem\r\n                                      onClick={() =>\r\n                                        handleSelectNewLeader(member.userId)\r\n                                      }\r\n                                      className=\"text-orange-500 focus:text-orange-500\"\r\n                                    >\r\n                                      <Crown className=\"h-4 w-4 mr-2\" />\r\n                                      Nhượng quyền trưởng nhóm\r\n                                    </DropdownMenuItem>\r\n                                  )}\r\n                                <DropdownMenuSeparator />\r\n                                <DropdownMenuItem\r\n                                  onClick={() =>\r\n                                    handleKickMember(member.userId)\r\n                                  }\r\n                                  className=\"text-red-500 focus:text-red-500\"\r\n                                >\r\n                                  <Ban className=\"h-4 w-4 mr-2\" />\r\n                                  Xóa khỏi nhóm\r\n                                </DropdownMenuItem>\r\n                              </>\r\n                            )}\r\n                          </DropdownMenuContent>\r\n                        </DropdownMenu>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n        </div>\r\n\r\n        {showProfileDialog && selectedMember && (\r\n          <ProfileDialog\r\n            user={selectedMember}\r\n            isOpen={showProfileDialog}\r\n            onOpenChange={(open) => {\r\n              setShowProfileDialog(open);\r\n              if (!open) {\r\n                setSelectedMember(null);\r\n                setShowFriendRequestForm(false);\r\n              }\r\n            }}\r\n            isOwnProfile={selectedMember.id === currentUser?.id}\r\n            initialShowFriendRequestForm={showFriendRequestForm}\r\n          />\r\n        )}\r\n\r\n        {/* Promote Member Confirmation Dialog */}\r\n        <AlertDialog\r\n          open={showPromoteDialog}\r\n          onOpenChange={setShowPromoteDialog}\r\n        >\r\n          <AlertDialogContent>\r\n            <AlertDialogHeader>\r\n              <AlertDialogTitle>Thăng cấp thành viên</AlertDialogTitle>\r\n              <AlertDialogDescription>\r\n                Bạn có chắc chắn muốn thăng cấp thành viên này lên phó nhóm? Họ\r\n                sẽ có quyền quản lý nhóm.\r\n              </AlertDialogDescription>\r\n            </AlertDialogHeader>\r\n            <AlertDialogFooter>\r\n              <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n              <AlertDialogAction\r\n                onClick={executePromoteMember}\r\n                disabled={isProcessing}\r\n                className=\"bg-blue-500 hover:bg-blue-600\"\r\n              >\r\n                {isProcessing ? (\r\n                  <>\r\n                    <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                    Đang xử lý...\r\n                  </>\r\n                ) : (\r\n                  \"Thăng cấp\"\r\n                )}\r\n              </AlertDialogAction>\r\n            </AlertDialogFooter>\r\n          </AlertDialogContent>\r\n        </AlertDialog>\r\n\r\n        {/* Demote Member Confirmation Dialog */}\r\n        <AlertDialog open={showDemoteDialog} onOpenChange={setShowDemoteDialog}>\r\n          <AlertDialogContent>\r\n            <AlertDialogHeader>\r\n              <AlertDialogTitle>Hạ cấp thành viên</AlertDialogTitle>\r\n              <AlertDialogDescription>\r\n                Bạn có chắc chắn muốn hạ cấp phó nhóm này xuống thành viên\r\n                thường? Họ sẽ mất quyền quản lý nhóm.\r\n              </AlertDialogDescription>\r\n            </AlertDialogHeader>\r\n            <AlertDialogFooter>\r\n              <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n              <AlertDialogAction\r\n                onClick={executeDemoteMember}\r\n                disabled={isProcessing}\r\n                className=\"bg-blue-500 hover:bg-blue-600\"\r\n              >\r\n                {isProcessing ? (\r\n                  <>\r\n                    <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                    Đang xử lý...\r\n                  </>\r\n                ) : (\r\n                  \"Hạ cấp\"\r\n                )}\r\n              </AlertDialogAction>\r\n            </AlertDialogFooter>\r\n          </AlertDialogContent>\r\n        </AlertDialog>\r\n\r\n        {/* Kick Member Confirmation Dialog */}\r\n        <AlertDialog open={showKickDialog} onOpenChange={setShowKickDialog}>\r\n          <AlertDialogContent>\r\n            <AlertDialogHeader>\r\n              <AlertDialogTitle>Xóa thành viên</AlertDialogTitle>\r\n              <AlertDialogDescription>\r\n                Bạn có chắc chắn muốn xóa thành viên này khỏi nhóm? Họ sẽ không\r\n                thể xem tin nhắn trong nhóm này nữa.\r\n              </AlertDialogDescription>\r\n            </AlertDialogHeader>\r\n            <AlertDialogFooter>\r\n              <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n              <AlertDialogAction\r\n                onClick={executeKickMember}\r\n                disabled={isProcessing}\r\n                className=\"bg-red-500 hover:bg-red-600\"\r\n              >\r\n                {isProcessing ? (\r\n                  <>\r\n                    <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                    Đang xử lý...\r\n                  </>\r\n                ) : (\r\n                  \"Xóa thành viên\"\r\n                )}\r\n              </AlertDialogAction>\r\n            </AlertDialogFooter>\r\n          </AlertDialogContent>\r\n        </AlertDialog>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={`h-full flex flex-col bg-white ${!isOverlay ? \"border-l\" : \"\"}`}\r\n    >\r\n      {/* Socket handler for real-time updates */}\r\n      {group && (\r\n        <GroupInfoSocketHandler\r\n          groupId={group.id}\r\n          onGroupUpdated={updateMembersList}\r\n        />\r\n      )}\r\n      <div className=\"p-4 flex items-center justify-between border-b\">\r\n        <h2 className=\"font-semibold\">Thông tin nhóm</h2>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"rounded-full\"\r\n            onClick={handleRefreshGroup}\r\n            title=\"Làm mới dữ liệu nhóm\"\r\n          >\r\n            <RefreshCw className=\"h-4 w-4\" />\r\n          </Button>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className={`${isOverlay ? \"bg-gray-100 hover:bg-gray-200\" : \"rounded-full\"}`}\r\n            onClick={onClose}\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      <ScrollArea className=\"flex-1\">\r\n        <div className=\"space-y-2 bg-[#ebecf0]\">\r\n          {/* Thông tin nhóm */}\r\n          <div className=\"flex flex-col items-center text-center bg-white p-2\">\r\n            <Avatar\r\n              className=\"h-20 w-20 mb-3 cursor-pointer\"\r\n              onClick={() => setShowGroupDialog(true)}\r\n            >\r\n              <AvatarImage\r\n                src={group.avatarUrl || undefined}\r\n                className=\"object-cover\"\r\n              />\r\n              <AvatarFallback className=\"text-xl\">\r\n                {group.name?.slice(0, 2).toUpperCase() || \"GR\"}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"flex items-center justify-center gap-2\">\r\n              <h2 className=\"text-lg font-semibold\">{group.name}</h2>\r\n              {currentUserRole === \"LEADER\" && (\r\n                <button\r\n                  className=\"text-gray-500 hover:text-blue-500 transition-colors\"\r\n                  onClick={() => setShowEditNameDialog(true)}\r\n                >\r\n                  <Pencil className=\"h-4 w-4\" />\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Các chức năng chính */}\r\n            <div className=\"grid grid-cols-4 gap-4 w-full m-2\">\r\n              <div className=\"flex flex-col items-center\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\r\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\r\n                >\r\n                  <Bell className=\"h-6 w-6\" />\r\n                </Button>\r\n                <span className=\"text-xs\">Bật thông báo</span>\r\n              </div>\r\n\r\n              <div className=\"flex flex-col items-center\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\r\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\r\n                >\r\n                  <Pin className=\"h-6 w-6\" />\r\n                </Button>\r\n                <span className=\"text-xs\">Ghim hội thoại</span>\r\n              </div>\r\n\r\n              <div className=\"flex flex-col items-center\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1\"\r\n                  onClick={() => {\r\n                    setShowAddMemberDialog(true);\r\n                  }}\r\n                >\r\n                  <UserPlus className=\"h-6 w-6\" />\r\n                </Button>\r\n                <span className=\"text-xs\">Thêm thành viên</span>\r\n              </div>\r\n\r\n              <div className=\"flex flex-col items-center\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\r\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\r\n                >\r\n                  <Settings className=\"h-6 w-6\" />\r\n                </Button>\r\n                <span className=\"text-xs\">Quản lý nhóm</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Thành viên nhóm */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">Thành viên nhóm</span>\r\n                <span className=\"text-xs text-gray-500 ml-2\">\r\n                  ({memberCount})\r\n                </span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              <div\r\n                className=\"p-3 flex items-center hover:bg-gray-50 cursor-pointer\"\r\n                onClick={() => setShowMembersList(true)}\r\n              >\r\n                <Users className=\"h-5 w-5 mr-2 text-gray-500\" />\r\n                <span className=\"text-sm\">{memberCount} thành viên</span>\r\n              </div>\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* Mã QR */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">Mã QR</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              <div\r\n                className=\"p-3 flex items-center hover:bg-gray-50 cursor-pointer\"\r\n                onClick={() => setShowGroupQRDialog(true)}\r\n              >\r\n                <QrCode className=\"h-5 w-5 mr-2 text-gray-500\" />\r\n                <span className=\"text-sm\">Mã QR nhóm</span>\r\n              </div>\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* Ảnh/Video */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">Ảnh/Video</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              {isLoadingMedia ? (\r\n                <div className=\"p-4 text-center\">\r\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\r\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\r\n                </div>\r\n              ) : mediaFiles.length > 0 ? (\r\n                <div className=\"px-3 pt-2 pb-1\">\r\n                  <div className=\"grid grid-cols-4 gap-1\">\r\n                    {mediaFiles.slice(0, 8).map((media, index) => (\r\n                      <div\r\n                        key={index}\r\n                        className=\"aspect-square relative overflow-hidden border border-gray-200 rounded-md cursor-pointer\"\r\n                        onClick={() => {\r\n                          setSelectedMediaIndex(index);\r\n                          setShowMediaViewer(true);\r\n                        }}\r\n                        title={media.fileName || \"Xem ảnh/video\"}\r\n                      >\r\n                        {media.metadata?.extension?.match(/mp4|webm|mov/i) ||\r\n                        media.type === \"VIDEO\" ? (\r\n                          <div className=\"w-full h-full relative\">\r\n                            <video\r\n                              className=\"w-full h-full object-cover\"\r\n                              src={media.url}\r\n                              muted\r\n                              preload=\"metadata\"\r\n                            />\r\n                            <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n                              <Video className=\"h-5 w-5 text-white\" />\r\n                            </div>\r\n                          </div>\r\n                        ) : (\r\n                          <div\r\n                            className=\"w-full h-full bg-cover bg-center\"\r\n                            style={{ backgroundImage: `url(${media.url})` }}\r\n                          ></div>\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"mt-2 px-2\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\r\n                      onClick={() => {\r\n                        setActiveGalleryTab(\"media\");\r\n                        setShowMediaGallery(true);\r\n                      }}\r\n                    >\r\n                      Xem tất cả\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-4 text-center\">\r\n                  <p className=\"text-sm text-gray-500\">\r\n                    Không có ảnh hoặc video nào\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* File */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">File</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              {isLoadingMedia ? (\r\n                <div className=\"p-4 text-center\">\r\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\r\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\r\n                </div>\r\n              ) : documents.length > 0 ? (\r\n                <div className=\"space-y-2 pb-2\">\r\n                  {documents.slice(0, 3).map((doc, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"flex items-center py-2 px-3 hover:bg-gray-200 cursor-pointer group\"\r\n                      onClick={() => window.open(doc.url, \"_blank\")}\r\n                      title={doc.fileName} // Add tooltip for full filename\r\n                    >\r\n                      <div className=\"bg-blue-100 p-2 rounded-md mr-2 flex-shrink-0\">\r\n                        <FileImage className=\"h-4 w-4 text-blue-500\" />\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0 mr-1\">\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <p className=\"font-medium text-sm truncate max-w-[160px]\">\r\n                            {doc.fileName}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-500 flex-shrink-0 ml-1\">\r\n                            {doc.metadata?.sizeFormatted ||\r\n                              `${Math.round((doc.metadata?.size || 0) / 1024)} KB`}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"icon\"\r\n                        className=\"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                      >\r\n                        <ChevronRight className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  ))}\r\n                  {documents.length > 3 && (\r\n                    <div className=\"mt-2 px-2 text-center\">\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\r\n                        onClick={() => {\r\n                          setActiveGalleryTab(\"files\");\r\n                          setShowMediaGallery(true);\r\n                        }}\r\n                      >\r\n                        Xem tất cả\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-4 text-center\">\r\n                  <p className=\"text-sm text-gray-500\">Không có file nào</p>\r\n                </div>\r\n              )}\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* Link */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">Link</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              {isLoadingMedia ? (\r\n                <div className=\"p-4 text-center\">\r\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\r\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\r\n                </div>\r\n              ) : links.length > 0 ? (\r\n                <div className=\"space-y-2 pb-2\">\r\n                  {links.slice(0, 3).map((link, index) => {\r\n                    // Extract domain from URL\r\n                    const domain = link.url\r\n                      .replace(/^https?:\\/\\//, \"\")\r\n                      .split(\"/\")[0];\r\n                    // Format date as DD/MM\r\n                    const date = new Date(link.timestamp);\r\n                    const formattedDate = `${date.getDate().toString().padStart(2, \"0\")}/${(date.getMonth() + 1).toString().padStart(2, \"0\")}`;\r\n\r\n                    return (\r\n                      <div\r\n                        key={index}\r\n                        className=\"flex items-center py-2 px-3 hover:bg-gray-200 cursor-pointer group\"\r\n                        onClick={() =>\r\n                          window.open(link.url, \"_blank\", \"noopener,noreferrer\")\r\n                        }\r\n                        title={link.title} // Add tooltip for full title\r\n                      >\r\n                        <div className=\"w-8 h-8 rounded-md mr-2 flex items-center justify-center overflow-hidden flex-shrink-0\">\r\n                          {getLinkIcon(domain)}\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <p className=\"font-medium text-sm truncate max-w-[160px]\">\r\n                              {getLinkTitle(\r\n                                domain,\r\n                                link.title.length > 30\r\n                                  ? link.title.substring(0, 30) + \"...\"\r\n                                  : link.title,\r\n                              )}\r\n                            </p>\r\n                            <p className=\"text-xs text-gray-500 flex-shrink-0 ml-1\">\r\n                              {formattedDate}\r\n                            </p>\r\n                          </div>\r\n                          <p className=\"text-xs text-blue-500 truncate max-w-[180px]\">\r\n                            {domain}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                  {links.length > 3 && (\r\n                    <div className=\"mt-2 px-2 text-center\">\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\r\n                        onClick={() => {\r\n                          setActiveGalleryTab(\"links\");\r\n                          setShowMediaGallery(true);\r\n                        }}\r\n                      >\r\n                        Xem tất cả\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-4 text-center\">\r\n                  <p className=\"text-sm text-gray-500\">Không có link nào</p>\r\n                </div>\r\n              )}\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* Cài đặt nhóm */}\r\n          <div className=\"space-y-1 bg-white p-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              className=\"w-full justify-start text-red-500 pl-2 opacity-60\"\r\n              onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\r\n            >\r\n              <Trash className=\"h-5 w-5 mr-3\" />\r\n              <span>Xóa lịch sử trò chuyện</span>\r\n            </Button>\r\n\r\n            {/* Nút chuyển quyền trưởng nhóm chỉ hiển thị cho trưởng nhóm và khi có nhiều hơn 1 thành viên */}\r\n            {currentUserRole === \"LEADER\" && group.members && group.members.length > 1 && (\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"w-full justify-start text-blue-500 pl-2\"\r\n                onClick={() => setShowTransferLeadershipDialog(true)}\r\n              >\r\n                <Shield className=\"h-5 w-5 mr-3\" />\r\n                <span>Chuyển quyền trưởng nhóm</span>\r\n              </Button>\r\n            )}\r\n\r\n            {/* Nút giải tán nhóm chỉ hiển thị cho trưởng nhóm */}\r\n            {currentUserRole === \"LEADER\" && (\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"w-full justify-start text-red-500 pl-2\"\r\n                onClick={() => setShowDeleteDialog(true)}\r\n              >\r\n                <Trash className=\"h-5 w-5 mr-3\" />\r\n                <span>Giải tán nhóm</span>\r\n              </Button>\r\n            )}\r\n\r\n            {/* Nút rời nhóm hiển thị cho tất cả thành viên, trừ khi trưởng nhóm là thành viên duy nhất */}\r\n            {!(currentUserRole === \"LEADER\" && group.members?.length === 1) && (\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"w-full justify-start text-red-500 pl-2\"\r\n                onClick={() => {\r\n                  // Nếu là trưởng nhóm, hiển thị dialog chuyển quyền trưởng nhóm\r\n                  if (currentUserRole === \"LEADER\") {\r\n                    setShowTransferLeadershipDialog(true);\r\n                  } else {\r\n                    setShowLeaveDialog(true);\r\n                  }\r\n                }}\r\n              >\r\n                <LogOut className=\"h-5 w-5 mr-3\" />\r\n                <span>Rời nhóm</span>\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </ScrollArea>\r\n\r\n      {showProfileDialog && selectedMember && (\r\n        <ProfileDialog\r\n          user={selectedMember}\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={setShowProfileDialog}\r\n          isOwnProfile={selectedMember.id === currentUser?.id}\r\n        />\r\n      )}\r\n\r\n      {/* Alert Dialog xác nhận xóa nhóm */}\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>\r\n              Bạn có chắc chắn muốn xóa nhóm này?\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Hành động này không thể hoàn tác. Tất cả tin nhắn và dữ liệu của\r\n              nhóm sẽ bị xóa vĩnh viễn.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleDeleteGroup}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? \"Đang xử lý...\" : \"Xóa nhóm\"}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Alert Dialog xác nhận rời nhóm */}\r\n      <AlertDialog open={showLeaveDialog} onOpenChange={setShowLeaveDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>\r\n              Bạn có chắc chắn muốn rời nhóm này?\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn sẽ không thể xem tin nhắn của nhóm này nữa trừ khi được mời\r\n              lại.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleLeaveGroup}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? \"Đang xử lý...\" : \"Rời nhóm\"}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Dialog chuyển quyền trưởng nhóm */}\r\n      <AlertDialog\r\n        open={showTransferLeadershipDialog}\r\n        onOpenChange={setShowTransferLeadershipDialog}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Chuyển quyền trưởng nhóm</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Người được chọn sẽ trở thành trưởng nhóm và có mọi quyền quản lý nhóm.\r\n              Bạn sẽ trở thành thành viên thường trong nhóm. Vui lòng chọn một thành viên\r\n              để trở thành trưởng nhóm mới.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <div className=\"max-h-[200px] overflow-y-auto my-4 border rounded-md\">\r\n            {group.members\r\n              ?.filter((member) => member.userId !== currentUser?.id) // Lọc ra các thành viên khác\r\n              .map((member) => {\r\n                const memberData = memberDetails[member.userId];\r\n                const initials = memberData?.userInfo?.fullName\r\n                  ? memberData.userInfo.fullName.slice(0, 2).toUpperCase()\r\n                  : \"??\";\r\n\r\n                return (\r\n                  <div\r\n                    key={`transfer-${member.userId}`}\r\n                    className=\"flex items-center p-3 hover:bg-gray-100 cursor-pointer\"\r\n                    onClick={() => handleSelectNewLeader(member.userId)}\r\n                  >\r\n                    <Avatar className=\"h-8 w-8 mr-3\">\r\n                      <AvatarImage\r\n                        src={\r\n                          memberData?.userInfo?.profilePictureUrl || undefined\r\n                        }\r\n                        className=\"object-cover\"\r\n                      />\r\n                      <AvatarFallback className=\"bg-gray-200 text-gray-600\">\r\n                        {initials}\r\n                      </AvatarFallback>\r\n                    </Avatar>\r\n                    <div>\r\n                      <p className=\"font-medium\">\r\n                        {memberData?.userInfo?.fullName || \"Thành viên\"}\r\n                      </p>\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        {member.role === \"CO_LEADER\"\r\n                          ? \"Phó nhóm\"\r\n                          : \"Thành viên\"}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n          </div>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Dialog xác nhận chuyển quyền trưởng nhóm */}\r\n      <AlertDialog\r\n        open={showConfirmTransferDialog}\r\n        onOpenChange={setShowConfirmTransferDialog}\r\n      >\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>\r\n              Xác nhận chuyển quyền trưởng nhóm\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              {newLeaderId && memberDetails[newLeaderId] ? (\r\n                <>\r\n                  Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho{\" \"}\r\n                  <strong>\r\n                    {memberDetails[newLeaderId]?.userInfo?.fullName ||\r\n                      \"Thành viên này\"}\r\n                  </strong>\r\n                  ?\r\n                  <br />\r\n                  Sau khi chuyển quyền, bạn sẽ trở thành thành viên thường trong\r\n                  nhóm.\r\n                </>\r\n              ) : (\r\n                \"Bạn có chắc chắn muốn chuyển quyền trưởng nhóm cho thành viên này?\"\r\n              )}\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel\r\n              disabled={isProcessing}\r\n              onClick={() => {\r\n                setShowConfirmTransferDialog(false);\r\n                setNewLeaderId(null);\r\n              }}\r\n            >\r\n              Hủy\r\n            </AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={executeTransferLeadership}\r\n              disabled={isProcessing}\r\n              className=\"bg-blue-500 hover:bg-blue-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xác nhận\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n      {showAddMemberDialog && group && (\r\n        <AddMemberDialog\r\n          isOpen={showAddMemberDialog}\r\n          onOpenChange={setShowAddMemberDialog}\r\n          groupId={group.id}\r\n        />\r\n      )}\r\n\r\n      {/* Group Dialog */}\r\n      {showGroupDialog && group && (\r\n        <GroupDialog\r\n          group={group}\r\n          isOpen={showGroupDialog}\r\n          onOpenChange={setShowGroupDialog}\r\n          mediaFiles={mediaFiles}\r\n        />\r\n      )}\r\n\r\n      {/* Edit Group Name Dialog */}\r\n      {group && (\r\n        <EditGroupNameDialog\r\n          group={group}\r\n          isOpen={showEditNameDialog}\r\n          onOpenChange={setShowEditNameDialog}\r\n          onBack={() => setShowEditNameDialog(false)}\r\n          onSuccess={(updatedGroup) => {\r\n            // Update the group in the store\r\n            const chatStore = useChatStore.getState();\r\n            if (chatStore.selectedGroup?.id === updatedGroup.id) {\r\n              chatStore.setSelectedGroup(updatedGroup);\r\n            }\r\n\r\n            // Refresh the page after a short delay to ensure all components are updated\r\n            setTimeout(() => {\r\n              window.location.reload();\r\n            }, 500);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Group QR Code Dialog */}\r\n      {group && (\r\n        <GroupQRCodeDialog\r\n          isOpen={showGroupQRDialog}\r\n          onClose={() => setShowGroupQRDialog(false)}\r\n          groupId={group.id}\r\n          groupName={group.name}\r\n        />\r\n      )}\r\n\r\n      {/* Media Viewer */}\r\n      {showMediaViewer && mediaFiles.length > 0 && (\r\n        <MediaViewer\r\n          isOpen={showMediaViewer}\r\n          onClose={() => setShowMediaViewer(false)}\r\n          media={mediaFiles.map((media) => ({\r\n            ...media,\r\n            // Ensure type is set correctly for videos\r\n            type:\r\n              media.metadata?.extension?.match(/mp4|webm|mov/i) ||\r\n              media.type === \"VIDEO\"\r\n                ? \"VIDEO\"\r\n                : \"IMAGE\",\r\n          }))}\r\n          initialIndex={selectedMediaIndex}\r\n          chatName={group.name || \"Nhóm chat\"}\r\n        />\r\n      )}\r\n\r\n      {/* Promote Member Confirmation Dialog */}\r\n      <AlertDialog open={showPromoteDialog} onOpenChange={setShowPromoteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Thăng cấp thành viên</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn thăng cấp thành viên này lên phó nhóm? Họ sẽ\r\n              có quyền quản lý nhóm.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={executePromoteMember}\r\n              disabled={isProcessing}\r\n              className=\"bg-blue-500 hover:bg-blue-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Thăng cấp\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Demote Member Confirmation Dialog */}\r\n      <AlertDialog open={showDemoteDialog} onOpenChange={setShowDemoteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Hạ cấp thành viên</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn hạ cấp phó nhóm này xuống thành viên thường?\r\n              Họ sẽ mất quyền quản lý nhóm.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={executeDemoteMember}\r\n              disabled={isProcessing}\r\n              className=\"bg-blue-500 hover:bg-blue-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Hạ cấp\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n\r\n      {/* Kick Member Confirmation Dialog */}\r\n      <AlertDialog open={showKickDialog} onOpenChange={setShowKickDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Xóa thành viên</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Bạn có chắc chắn muốn xóa thành viên này khỏi nhóm? Họ sẽ không\r\n              thể xem tin nhắn trong nhóm này nữa.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel disabled={isProcessing}>Hủy</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={executeKickMember}\r\n              disabled={isProcessing}\r\n              className=\"bg-red-500 hover:bg-red-600\"\r\n            >\r\n              {isProcessing ? (\r\n                <>\r\n                  <div className=\"animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2\"></div>\r\n                  Đang xử lý...\r\n                </>\r\n              ) : (\r\n                \"Xóa thành viên\"\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n\r\n  // Hàm xử lý thăng cấp thành viên lên phó nhóm\r\n  async function handlePromoteMember(memberId: string) {\r\n    setSelectedMemberId(memberId);\r\n    setShowPromoteDialog(true);\r\n    setOpenDropdownMemberId(null); // Close dropdown after action\r\n  }\r\n\r\n  // Hàm thực hiện thăng cấp thành viên\r\n  async function executePromoteMember() {\r\n    if (!group?.id || !selectedMemberId) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        selectedMemberId,\r\n        GroupRole.CO_LEADER,\r\n      );\r\n      if (result.success) {\r\n        // Cập nhật UI hoặc reload dữ liệu nhóm\r\n        setShowPromoteDialog(false);\r\n\r\n        // Cập nhật vai trò trong state để UI hiển thị đúng\r\n        if (group.members) {\r\n          const updatedMembers = group.members.map((member) => {\r\n            if (member.userId === selectedMemberId) {\r\n              return { ...member, role: GroupRole.CO_LEADER };\r\n            }\r\n            return member;\r\n          });\r\n          group.members = updatedMembers;\r\n        }\r\n\r\n        // Force UI update by updating the group state\r\n        if (group.members) {\r\n          setGroup({ ...group, members: [...group.members] });\r\n        }\r\n\r\n        // Force refresh group data to ensure all components are updated\r\n        setTimeout(() => {\r\n          updateMembersList(true);\r\n        }, 500);\r\n\r\n        toast.success(\"Đã thăng cấp thành viên thành phó nhóm\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error promoting member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi thăng cấp thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  }\r\n\r\n  // Hàm xử lý hạ cấp phó nhóm xuống thành viên thường\r\n  async function handleDemoteMember(memberId: string) {\r\n    setSelectedMemberId(memberId);\r\n    setShowDemoteDialog(true);\r\n    setOpenDropdownMemberId(null); // Close dropdown after action\r\n  }\r\n\r\n  // Hàm thực hiện hạ cấp thành viên\r\n  async function executeDemoteMember() {\r\n    if (!group?.id || !selectedMemberId) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        selectedMemberId,\r\n        GroupRole.MEMBER,\r\n      );\r\n      if (result.success) {\r\n        // Cập nhật UI hoặc reload dữ liệu nhóm\r\n        setShowDemoteDialog(false);\r\n\r\n        // Cập nhật vai trò trong state để UI hiển thị đúng\r\n        if (group.members) {\r\n          const updatedMembers = group.members.map((member) => {\r\n            if (member.userId === selectedMemberId) {\r\n              return { ...member, role: GroupRole.MEMBER };\r\n            }\r\n            return member;\r\n          });\r\n          group.members = updatedMembers;\r\n        }\r\n\r\n        // Force UI update by updating the group state\r\n        if (group.members) {\r\n          setGroup({ ...group, members: [...group.members] });\r\n        }\r\n\r\n        // Force refresh group data to ensure all components are updated\r\n        setTimeout(() => {\r\n          updateMembersList(true);\r\n        }, 500);\r\n\r\n        toast.success(\"Đã hạ cấp thành viên xuống thành viên thường\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error demoting member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi hạ cấp thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  }\r\n\r\n  // Hàm xử lý xóa thành viên khỏi nhóm\r\n  async function handleKickMember(memberId: string) {\r\n    setSelectedMemberId(memberId);\r\n    setShowKickDialog(true);\r\n    setOpenDropdownMemberId(null); // Close dropdown after action\r\n    // Keep the member list open when showing the kick dialog\r\n    // This ensures the alert dialog appears on top of the member list\r\n  }\r\n\r\n  // Hàm thực hiện xóa thành viên\r\n  async function executeKickMember() {\r\n    if (!group?.id || !selectedMemberId) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await removeGroupMember(group.id, selectedMemberId);\r\n      if (result.success) {\r\n        // Cập nhật UI hoặc reload dữ liệu nhóm\r\n        setShowKickDialog(false);\r\n\r\n        // Cập nhật danh sách thành viên trong state để UI hiển thị đúng\r\n        if (group.members) {\r\n          const updatedMembers = group.members.filter(\r\n            (member) => member.userId !== selectedMemberId,\r\n          );\r\n          group.members = updatedMembers;\r\n        }\r\n\r\n        // Force UI update by updating the group state\r\n        if (group.members) {\r\n          setGroup({ ...group, members: [...group.members] });\r\n        }\r\n\r\n        toast.success(\"Đã xóa thành viên khỏi nhóm\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error removing member:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi xóa thành viên\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  }\r\n\r\n  // Hàm xử lý xóa nhóm\r\n  async function handleDeleteGroup() {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await deleteGroup(group.id);\r\n      if (result.success) {\r\n        // Đóng dialog và chuyển hướng về trang chat\r\n        setShowDeleteDialog(false);\r\n\r\n        // Đóng chat của nhóm này\r\n        const chatStore = useChatStore.getState();\r\n\r\n        // Xóa cache của nhóm này\r\n        chatStore.clearChatCache(\"GROUP\", group.id);\r\n\r\n        // Đặt selectedGroup về null để đóng chat\r\n        chatStore.setSelectedGroup(null);\r\n\r\n        // Xóa nhóm khỏi danh sách cuộc trò chuyện\r\n        const conversationsStore = useConversationsStore.getState();\r\n        conversationsStore.removeConversation(group.id);\r\n\r\n        // Đóng dialog thông tin nhóm\r\n        onClose();\r\n\r\n        // Thông báo cho người dùng\r\n        toast.success(\"Đã giải tán nhóm thành công\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi giải tán nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  }\r\n\r\n  // Hàm xử lý khi chọn thành viên để chuyển quyền trưởng nhóm\r\n  function handleSelectNewLeader(memberId: string) {\r\n    setNewLeaderId(memberId);\r\n    setShowConfirmTransferDialog(true);\r\n  }\r\n\r\n  // Hàm xử lý chuyển quyền trưởng nhóm\r\n  async function executeTransferLeadership() {\r\n    if (!group?.id || !newLeaderId) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      // Chuyển quyền trưởng nhóm cho thành viên được chọn\r\n      const result = await updateMemberRole(\r\n        group.id,\r\n        newLeaderId,\r\n        GroupRole.LEADER,\r\n      );\r\n\r\n      if (result.success) {\r\n        // Đóng các dialog\r\n        setShowConfirmTransferDialog(false);\r\n        setShowTransferLeadershipDialog(false);\r\n\r\n        // Cập nhật vai trò trong state để UI hiển thị đúng\r\n        if (group.members && currentUser) {\r\n          const updatedMembers = group.members.map((member) => {\r\n            if (member.userId === newLeaderId) {\r\n              return { ...member, role: GroupRole.LEADER };\r\n            }\r\n            if (member.userId === currentUser.id) {\r\n              return { ...member, role: GroupRole.MEMBER };\r\n            }\r\n            return member;\r\n          });\r\n          group.members = updatedMembers;\r\n\r\n          // Cập nhật vai trò của người dùng hiện tại\r\n          setCurrentUserRole(GroupRole.MEMBER);\r\n        }\r\n\r\n        // Force UI update by updating the group state\r\n        if (group.members) {\r\n          setGroup({ ...group, members: [...group.members] });\r\n        }\r\n\r\n        // Force refresh group data to ensure all components are updated\r\n        setTimeout(() => {\r\n          updateMembersList(true);\r\n        }, 500);\r\n\r\n        // Thông báo cho người dùng\r\n        toast.success(\"Đã chuyển quyền trưởng nhóm thành công\");\r\n\r\n        // Reset newLeaderId\r\n        setNewLeaderId(null);\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error transferring leadership:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi chuyển quyền trưởng nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  }\r\n\r\n  // Hàm xử lý rời nhóm\r\n  async function handleLeaveGroup() {\r\n    if (!group?.id) return;\r\n    setIsProcessing(true);\r\n    try {\r\n      const result = await leaveGroup(group.id);\r\n      if (result.success) {\r\n        // Đóng dialog xác nhận\r\n        setShowLeaveDialog(false);\r\n\r\n        // Đóng chat của nhóm này\r\n        const chatStore = useChatStore.getState();\r\n\r\n        // Xóa cache của nhóm này\r\n        chatStore.clearChatCache(\"GROUP\", group.id);\r\n\r\n        // Đặt selectedGroup về null để đóng chat\r\n        chatStore.setSelectedGroup(null);\r\n\r\n        // Xóa nhóm khỏi danh sách cuộc trò chuyện\r\n        const conversationsStore = useConversationsStore.getState();\r\n        conversationsStore.removeConversation(group.id);\r\n\r\n        // Đóng dialog thông tin nhóm\r\n        onClose();\r\n\r\n        // Thông báo cho người dùng\r\n        toast.success(\"Đã rời nhóm thành công\");\r\n      } else {\r\n        toast.error(`Lỗi: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error leaving group:\", error);\r\n      toast.error(\"Đã xảy ra lỗi khi rời nhóm\");\r\n    } finally {\r\n      setIsProcessing(false);\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AAEA;AACA;AAIA;AAUA;AAOA;AAOA;AACA;AA9EA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsFe,SAAS,UAAU,EAChC,OAAO,YAAY,EACnB,OAAO,EACP,YAAY,KAAK,EACF;IACf,2EAA2E;IAC3E,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAEjE,iDAAiD;IACjD,6EAA6E;IAC7E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/B,iBAAiB;IAGnB,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mDAAmD;QACnD,IAAI,CAAC,OAAO,6BAA6B,EAAE;YACzC,OAAO,6BAA6B,GAAG,CAAC;QAC1C;QAEA,MAAM,UAAU,eAAe,MAAM,cAAc;QACnD,IAAI,CAAC,SAAS;QAEd,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,iBAAiB,OAAO,6BAA6B,CAAC,QAAQ,IAAI;QACxE,MAAM,sBAAsB,MAAM;QAElC,4CAA4C;QAC5C,IAAI,sBAAsB,MAAM;YAC9B,QAAQ,GAAG,CACT,CAAC,mDAAmD,EAAE,oBAAoB,MAAM,CAAC;YAEnF;QACF;QAEA,wCAAwC;QACxC,OAAO,6BAA6B,CAAC,QAAQ,GAAG;QAEhD,yCAAyC;QACzC,IAAI,eAAe;YACjB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CACT,8BACA,cAAc,OAAO,EAAE,UAAU;YAEnC,SAAS;QACX,OAAO,IAAI,cAAc;YACvB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CACT,8BACA,aAAa,OAAO,EAAE,UAAU;YAElC,SAAS;QACX;IACF,GAAG;QAAC;QAAe;KAAa;IAChC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzC,EAAE;IAEJ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvC,EAAE;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE/B,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;QACpB,mBAAmB;IACrB,GAAG;QAAC,OAAO;KAAG;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE9C,CAAC;IACJ,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAC3E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,CAAC;IAEH,MAAM,CAAC,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,8BAA8B,gCAAgC,GACnE,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,2BAA2B,6BAA6B,GAC7D,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACX,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnD;IAEF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE7D;IACF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAErD;IACF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,QAAQ;IACvD,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,IAAI;IACtD,wCAAwC;IACxC,6DAA6D;IAE7D,8FAA8F;IAC9F,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,OAAO,eAAe,KAAK;QACzB,MAAM,UAAU,OAAO,MAAM,eAAe,MAAM,cAAc;QAChE,IAAI,CAAC,SAAS,OAAO;QAErB,kDAAkD;QAClD,IAAI,CAAC,OAAO,yBAAyB,EAAE;YACrC,OAAO,yBAAyB,GAAG,CAAC;QACtC;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,eAAe,OAAO,yBAAyB,CAAC,QAAQ,IAAI;QAClE,MAAM,oBAAoB,MAAM;QAEhC,0EAA0E;QAC1E,IAAI,oBAAoB,QAAQ,CAAC,cAAc;YAC7C,QAAQ,GAAG,CACT,CAAC,6CAA6C,EAAE,kBAAkB,MAAM,CAAC;YAE3E,OAAO;QACT;QAEA,6BAA6B;QAC7B,OAAO,yBAAyB,CAAC,QAAQ,GAAG;QAE5C,QAAQ,GAAG,CACT,+CACA,SACA,iBACA;QAGF,gDAAgD;QAChD,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;QAIvC,MAAM,aAAa,UAAU,UAAU,GACnC,UAAU,UAAU,CAAC,QAAQ,GAC7B;QACJ,MAAM,cAAc,IAAI;QACxB,MAAM,eACJ,cACA,CAAC,gBAAgB,yCAAyC;QAC1D,YAAY,OAAO,KAAK,WAAW,WAAW,CAAC,OAAO,KAAK,KAAK,MAAM,mBAAmB;QAE3F,IAAI,cAAc;YAChB,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,SAAS;YAEhE,0CAA0C;YAC1C,SAAS,WAAW,KAAK;YAEzB,0DAA0D;YAC1D,OAAO;QACT;QAEA,IAAI;YACF,oEAAoE;YACpE,QAAQ,GAAG,CAAC;YACZ,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE;YAElC,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;gBAClC,QAAQ,GAAG,CACT;gBAEF,QAAQ,GAAG,CACT,8BACA,OAAO,KAAK,CAAC,OAAO,EAAE,UAAU;gBAElC,QAAQ,GAAG,CACT,sCACA,OAAO,SAAS,UAAU;gBAG5B,qDAAqD;gBACrD,MAAM,iBACJ,OAAO,SAAS,WAAW,OAAO,KAAK,CAAC,OAAO,EAAE;gBACnD,QAAQ,GAAG,CAAC,gCAAgC;gBAE5C,8CAA8C;gBAC9C,SAAS,OAAO,KAAK;gBAErB,qCAAqC;gBACrC,UAAU,gBAAgB,CAAC,OAAO,KAAK;gBAEvC,mBAAmB;gBACnB,IAAI,UAAU,UAAU,EAAE;oBACxB,yCAAyC;oBACzC,UAAU,UAAU,CAAC,QAAQ,GAAG;wBAC9B,OAAO,OAAO,KAAK;wBACnB,aAAa,IAAI;oBACnB;gBACF;gBAEA,mEAAmE;gBACnE,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,SAAS;oBAC3D,OAAO;wBACL,IAAI,OAAO,KAAK,CAAC,EAAE;wBACnB,MAAM,OAAO,KAAK,CAAC,IAAI;wBACvB,WAAW,OAAO,KAAK,CAAC,SAAS;wBACjC,WAAW,OAAO,KAAK,CAAC,SAAS;wBACjC,aAAa,OAAO,KAAK,CAAC,WAAW;oBACvC;gBACF;gBAEA,uDAAuD;gBACvD,IAAI,kBAAkB,CAAC,cAAc;oBACnC,IACE,CAAC,OAAO,SAAS,UAAU,CAAC,IAC5B,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,GAClC;wBACA,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACb,OAAO,IACL,CAAC,OAAO,SAAS,UAAU,CAAC,IAC5B,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,GAClC;wBACA,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oBACb;gBACF;gBAEA,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;QAEA,8DAA8D;QAC9D,MAAM,qBAAqB,UAAU,aAAa;QAClD,IAAI,sBAAsB,mBAAmB,EAAE,KAAK,SAAS;YAC3D,QAAQ,GAAG,CAAC;YACZ,SAAS;YACT,0DAA0D;YAC1D,OAAO;QACT;QAEA,OAAO;IACT,GACA;QAAC,OAAO;QAAI,OAAO,SAAS;QAAQ,eAAe;QAAI,cAAc;KAAG;IAG1E,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAEX,IAAI;YACF,qDAAqD;YACrD,MAAM,UAAU,MAAM,kBAAkB;YAExC,IAAI,SAAS;gBACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;YAEA,iFAAiF;YACjF,QAAQ,GAAG,CAAC;YAEZ,qCAAqC;YACrC,MAAM,UAAU,OAAO,MAAM,eAAe,MAAM,cAAc;YAChE,IAAI,SAAS;gBACX,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,uCAAuC;gBACvC,IAAI,UAAU,UAAU,IAAI,UAAU,UAAU,CAAC,QAAQ,EAAE;oBACzD,OAAO,UAAU,UAAU,CAAC,QAAQ;gBACtC;YACF;YAEA,MAAM,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,oBAAoB;YAElD,mDAAmD;YACnD,MAAM,uBAAuB,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,aAAa;YAElE,IAAI,wBAAwB,qBAAqB,EAAE,KAAK,SAAS;gBAC/D,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CACT,kCACA,qBAAqB,OAAO,EAAE,UAAU;gBAG1C,uBAAuB;gBACvB,SAAS;gBAET,0DAA0D;gBAE1D,mEAAmE;gBACnE,IAAI,WAAW,sBAAsB;oBACnC,mIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,SAAS;wBAC3D,OAAO;4BACL,IAAI,qBAAqB,EAAE;4BAC3B,MAAM,qBAAqB,IAAI;4BAC/B,WAAW,qBAAqB,SAAS;4BACzC,WAAW,qBAAqB,SAAS;4BACzC,aAAa,qBAAqB,WAAW;wBAC/C;oBACF;gBACF;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF;YAEA,iEAAiE;YACjE,uCAAiE;;YAG/D,0DAA0D;YAC5D,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,yFAAyF;IACzF,sDAAsD;IACtD,MAAM,gBAAgB,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,QAAU,MAAM,aAAa;IAE1E,2CAA2C;IAC3C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,IAAI,CAAC,cAAc,IAAI,OAAO;QAC9B,OAAO,cAAc,IAAI,CACvB,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO,aAAa,EAAE;IAEzE,GAAG;QAAC;QAAe,cAAc;KAAG;IAEpC,sDAAsD;IACtD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,kDAAkD;QAClD,IAAI,mBAAmB,OAAO,aAAa;YACzC,OAAO,kBAAkB,KAAK,CAAC,WAAW,CAAC,MAAM;QACnD;QACA,iDAAiD;QACjD,OAAO,OAAO,SAAS,UAAU;IACnC,GAAG;QAAC,mBAAmB,OAAO;QAAa,OAAO;KAAQ;IAE1D,+EAA+E;IAC/E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,MAAM,MAAM,OAAO,EAAE;YAC9B,MAAM,qBAAqB;gBACzB,MAAM,mBAEF,CAAC;gBACL,MAAM,kBAA2C,CAAC;gBAClD,MAAM,mBAA8C,CAAC;gBAErD,IAAI;oBACF,+CAA+C;oBAC/C,MAAM,YAAsB,EAAE;oBAC9B,MAAM,WAAqB,EAAE;oBAC7B,MAAM,kBAA4B,EAAE;oBAEpC,gCAAgC;oBAChC,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;wBAClC,sCAAsC;wBACtC,IAAI,CAAC,OAAO,IAAI,EAAE,UAAU;4BAC1B,UAAU,IAAI,CAAC,OAAO,MAAM;wBAC9B,OAAO;4BACL,wCAAwC;4BACxC,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,IAAI;wBAG/C;wBAEA,uCAAuC;wBACvC,IACE,OAAO,OAAO,IACd,OAAO,OAAO,OAAO,KAAK,YAC1B,QAAQ,OAAO,OAAO,IACtB,cAAc,OAAO,OAAO,EAC5B;4BACA,2DAA2D;4BAC3D,MAAM,YAAY,OAAO,OAAO;4BAIhC,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;gCAC/B,IAAI,UAAU,EAAE;gCAChB,UAAU;oCACR,IAAI,UAAU,EAAE;oCAChB,UAAU,UAAU,QAAQ;oCAC5B,gBAAgB;oCAChB,WAAW,IAAI;oCACf,WAAW,IAAI;oCACf,UAAU;wCAAE,IAAI,UAAU,EAAE;oCAAC;gCAC/B;4BACF;wBACF,OAAO,IACL,OAAO,SAAS,IAChB,OAAO,SAAS,KAAK,aAAa,MAClC,CAAC,OAAO,OAAO,EACf;4BACA,SAAS,IAAI,CAAC,OAAO,SAAS;wBAChC,OAAO,IAAI,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,EAAE;4BACzD,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG,OAAO,OAAO;wBACjD;wBAEA,8CAA8C;wBAC9C,IAAI,OAAO,MAAM,KAAK,aAAa,IAAI;4BACrC,gBAAgB,IAAI,CAAC,OAAO,MAAM;wBACpC;wBAEA,wBAAwB;wBACxB,IAAI,eAAe,OAAO,MAAM,KAAK,YAAY,EAAE,EAAE;4BACnD,mBAAmB,OAAO,IAAI;wBAChC;oBACF;oBAEA,wBAAwB;oBACxB,IAAI,UAAU,MAAM,GAAG,GAAG;wBACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,eAAe,CAAC;wBAC/D,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC1C,IAAI,WAAW,OAAO,IAAI,WAAW,KAAK,EAAE;4BAC1C,WAAW,KAAK,CAAC,OAAO,CAAC,CAAC;gCACxB,gBAAgB,CAAC,KAAK,EAAE,CAAC,GAAG;4BAG9B;wBACF;oBACF;oBAEA,yBAAyB;oBACzB,IAAI,SAAS,MAAM,GAAG,GAAG;wBACvB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,cAAc,CAAC;wBAC7D,MAAM,cAAc,MAAM,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;wBAC3C,IAAI,YAAY,OAAO,IAAI,YAAY,KAAK,EAAE;4BAC5C,0BAA0B;4BAC1B,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;gCAClC,IAAI,OAAO,SAAS,EAAE;oCACpB,MAAM,QAAQ,YAAY,KAAK,CAAC,IAAI,CAClC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,SAAS;oCAElC,IAAI,OAAO;wCACT,eAAe,CAAC,OAAO,MAAM,CAAC,GAAG;oCACnC;gCACF;4BACF;wBACF;oBACF;oBAEA,gCAAgC;oBAChC,IAAI,gBAAgB,MAAM,GAAG,GAAG;wBAC9B,QAAQ,GAAG,CACT,CAAC,eAAe,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;wBAE1D,MAAM,cACJ,0HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,WAAW,IAAI;wBACzC,MAAM,qBAAqB,MAAM,CAAA,GAAA,sJAAA,CAAA,wBAAqB,AAAD,EACnD,iBACA;wBAGF,IACE,mBAAmB,OAAO,IAC1B,mBAAmB,aAAa,EAChC;4BACA,wBAAwB;4BACxB,OAAO,OAAO,CAAC,mBAAmB,aAAa,EAAE,OAAO,CACtD,CAAC,CAAC,QAAQ,KAAK;gCACb,gCAAgC;gCAChC,MAAM,SAAS,KAAK,MAAM,IAAI;gCAE9B,kCAAkC;gCAClC,IAAI,WAAW,cAAc,WAAW,UAAU;oCAChD,gBAAgB,CAAC,OAAO,GAAG;gCAC7B,OAAO,IAAI,WAAW,gBAAgB;oCACpC,gBAAgB,CAAC,OAAO,GAAG;gCAC7B,OAAO,IAAI,WAAW,oBAAoB;oCACxC,gBAAgB,CAAC,OAAO,GAAG;gCAC7B,OAAO;oCACL,gBAAgB,CAAC,OAAO,GAAG;gCAC7B;gCAEA,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EACzC,gBAAgB,CAAC,OAAO;4BAE5B;wBAEJ;oBACF;oBAEA,+DAA+D;oBAC/D,KAAK,MAAM,UAAU,MAAM,OAAO,CAAE;wBAClC,IACE,OAAO,MAAM,KAAK,aAAa,MAC/B,CAAC,gBAAgB,CAAC,OAAO,MAAM,CAAC,EAChC;4BACA,gBAAgB,CAAC,OAAO,MAAM,CAAC,GAAG;wBACpC;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAClD;gBAEA,8CAA8C;gBAC9C,iBAAiB;gBACjB,gBAAgB;gBAChB,iBAAiB;YACnB;YAEA;QACF;IACF,GAAG;QAAC,OAAO;QAAI,OAAO;QAAS;KAAY,GAAG,wCAAwC;IAEtF,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,IAAI;YACb,kBAAkB;YAElB,gCAAgC;YAChC,MAAM,2BAA2B;gBAC/B,IAAI;oBACF,MAAM,qBAIC,EAAE;oBACT,MAAM,gBAIC,EAAE;oBACT,MAAM,iBAIA,EAAE;oBAER,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW;wBAC5B,QAAQ,IAAI,CAAC,yCAAyC;wBACtD;oBACF;oBAEA,SAAS,OAAO,CAAC,CAAC;wBAChB,IAAI;4BACF,wBAAwB;4BACxB,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;gCAC3C,QAAQ,IAAI,CAAC,uCAAuC;gCACpD;4BACF;4BAEA,yBAAyB;4BACzB,IAAI,QAAQ,QAAQ,EAAE;4BAEtB,gCAAgC;4BAChC,IAAI,CAAC,QAAQ,OAAO,EAAE;gCACpB,QAAQ,IAAI,CAAC,wCAAwC;gCACrD;4BACF;4BAEA,gBAAgB;4BAChB,MAAM,QAAQ,QAAQ,OAAO,CAAC,KAAK;4BACnC,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,GAAG,GAAG;gCAC5C,MAAM,OAAO,CAAC,CAAC;oCACb,IAAI,CAAC,WAAW,UAAU,WAAW;wCACnC,QAAQ,IAAI,CACV,yDACA;wCAEF;oCACF;oCAEA,MAAM,YAAY,UAAU,QAAQ,CAAC,SAAS,CAAC,WAAW;oCAC1D,MAAM,gBAAgB;wCACpB,GAAG,SAAS;wCACZ,WAAW,IAAI,KAAK,QAAQ,SAAS,IAAI,KAAK,GAAG;wCACjD,QAAQ,QAAQ,MAAM;wCACtB,UAAU,QAAQ,QAAQ;oCAC5B;oCAEA,IACE;wCACE;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;wCACA;qCACD,CAAC,QAAQ,CAAC,YACX;wCACA,mBAAmB,IAAI,CAAC;oCAC1B,OAAO;wCACL,cAAc,IAAI,CAAC;oCACrB;gCACF;4BACF;4BAEA,wBAAwB;4BACxB,MAAM,OAAO,QAAQ,OAAO,CAAC,IAAI;4BACjC,IAAI,OAAO,SAAS,YAAY,KAAK,MAAM,GAAG,GAAG;gCAC/C,MAAM,WAAW;gCACjB,MAAM,UAAU,KAAK,KAAK,CAAC;gCAC3B,IAAI,SAAS;oCACX,QAAQ,OAAO,CAAC,CAAC;wCACf,IAAI;4CACF,yBAAyB;4CACzB,MAAM,SAAS,IACZ,OAAO,CAAC,gBAAgB,IACxB,KAAK,CAAC,IAAI,CAAC,EAAE;4CAChB,iDAAiD;4CACjD,MAAM,QAAQ,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;4CACnC,eAAe,IAAI,CAAC;gDAClB;gDACA;gDACA,WAAW,IAAI,KAAK,QAAQ,SAAS,IAAI,KAAK,GAAG;4CACnD;wCACF,EAAE,OAAO,OAAO;4CACd,QAAQ,IAAI,CACV,qCACA,KACA;wCAEJ;oCACF;gCACF;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,IAAI,CACV,yCACA,SACA;wBAEJ;oBACF;oBAEA,mCAAmC;oBACnC,mBAAmB,IAAI,CACrB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;oBAEvD,cAAc,IAAI,CAChB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;oBAEvD,eAAe,IAAI,CACjB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;oBAGvD,cAAc,mBAAmB,KAAK,CAAC,GAAG,MAAM,oBAAoB;oBACpE,aAAa,cAAc,KAAK,CAAC,GAAG,MAAM,oBAAoB;oBAC9D,SAAS,eAAe,KAAK,CAAC,GAAG,MAAM,oBAAoB;gBAC7D,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CACX,kDACA;gBAEJ,SAAU;oBACR,kBAAkB;gBACpB;YACF;YAEA;QACF;IACF,GAAG;QAAC,OAAO;QAAI;KAAS;IAExB,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAC3B,QAAQ,GAAG,CAAC;YACZ,2CAA2C;YAC3C,IAAI,SAAS;gBACX;YACF;QACF;IACF,GAAG;QAAC;QAAc;QAAO;KAAQ;IAEjC,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,MAAM,oBAAoB,OAAO;QAC/B,mEAAmE;QACnE,IAAI,aAAa,CAAC,SAAS,EAAE;YAC3B,kBAAkB,aAAa,CAAC,SAAS;YACzC,qBAAqB;YACrB;QACF;QAEA,4EAA4E;QAC5E,MAAM,iBAAiB,OAAO,SAAS,KACrC,CAAC,IAAM,EAAE,MAAM,KAAK,YAAY,EAAE,IAAI,EAAE;QAE1C,IAAI,gBAAgB,MAAM;YACxB,kBAAkB,eAAe,IAAI;YACrC,qBAAqB;YACrB;QACF;QAEA,IAAI;YACF,6CAA6C;YAC7C,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;YACrC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,kBAAkB,OAAO,IAAI;gBAC7B,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,6DAA6D;YAC7D,QAAQ,GAAG,CACT;QAEJ;IACF;IAEA,IAAI,kBAAkB;QACpB,qBACE,8OAAC,8IAAA,CAAA,UAAgB;YACf,YAAY;YACZ,WAAW;YACX,OAAO;YACP,YAAY;YACZ,SAAS,IAAM,oBAAoB;;;;;;IAGzC;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,MAAM,aAAa,aAAa,CAAC,OAAO;QACxC,IAAI,YAAY;YACd,kBAAkB;YAClB,yBAAyB;YACzB,qBAAqB;YACrB,wBAAwB,OAAO,8BAA8B;QAC/D;IACF;IAEA,IAAI,iBAAiB;QACnB,qBACE,8OAAC;YACC,WAAW,CAAC,8BAA8B,EAAE,CAAC,YAAY,aAAa,IAAI;;8BAE1E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,mBAAmB;0CAElC,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAIlC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAS;4BACP,mBAAmB;4BACnB,uBAAuB;wBACzB;;0CAEA,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;gCAAU;gCAAuB;gCAAY;;;;;;;sCAC7D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,OAAM;sCAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAIzB,8OAAC;oBAAI,WAAU;8BAEZ,mBAAmB,OAAO,cAEvB,kBAAkB,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBACvC,MAAM,WAAW,OAAO,QAAQ,GAC5B,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACvC;wBAEJ,qBACE,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,kBAAkB,OAAO,EAAE;;sDAE1C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDACV,KAAK,OAAO,iBAAiB,IAAI;oDACjC,WAAU;;;;;;8DAEZ,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DACV,OAAO,QAAQ,IAAI;;;;;;8DAEtB,8OAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,WACb,gBACA,OAAO,IAAI,KAAK,cACd,aACA;;;;;;;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;wCAEZ,OAAO,EAAE,KAAK,aAAa,MAC1B,aAAa,CAAC,OAAO,EAAE,CAAC,KAAK,gCAC3B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,QAAQ;4CACR,OAAM;sDAEN,cAAA,8OAAC,kMAAA,CAAA,OAAQ;gDAAC,WAAU;;;;;;;;;;;wCAKzB,OAAO,EAAE,KAAK,aAAa,oBAC1B,8OAAC,4IAAA,CAAA,eAAY;4CACX,MAAM,yBAAyB,OAAO,EAAE;4CACxC,cAAc,CAAC;gDACb,IAAI,MAAM;oDACR,wBAAwB,OAAO,EAAE;gDACnC,OAAO,IAAI,yBAAyB,OAAO,EAAE,EAAE;oDAC7C,wBAAwB;gDAC1B;4CACF;;8DAEA,8OAAC,4IAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,8OAAC,4IAAA,CAAA,sBAAmB;oDAClB,OAAM;oDACN,iBAAiB,IACf,wBAAwB;;wDAIzB,aAAa,CAAC,OAAO,EAAE,CAAC,KAAK,wBAC5B,8OAAC,4IAAA,CAAA,mBAAgB;4DACf,SAAS,IACP,wBAAwB,OAAO,EAAE;4DAEnC,UAAU,gBAAgB,CAAC,OAAO,EAAE,CAAC;sEAEpC,gBAAgB,CAAC,OAAO,EAAE,CAAC,iBAC1B;;kFACE,8OAAC;wEAAI,WAAU;;;;;;oEAA6F;;6FAI9G;;kFACE,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAA+B;;;;;;;;wDAQ1D,CAAC,oBAAoB,YACnB,oBAAoB,eACnB,OAAO,IAAI,KAAK,QAAS,mBAC3B;;gEACG,oBAAoB,YACnB,OAAO,IAAI,KAAK,0BACd,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,oBAAoB,OAAO,EAAE;;sFAG/B,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAIxC,oBAAoB,YACnB,OAAO,IAAI,KAAK,6BACd,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,mBAAmB,OAAO,EAAE;;sFAG9B,8OAAC,gNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAI3C,oBAAoB,YACnB,CAAC,OAAO,IAAI,KAAK,YACf,OAAO,IAAI,KAAK,WAAW,mBAC3B,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,sBAAsB,OAAO,EAAE;oEAEjC,WAAU;;sFAEV,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAIxC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8EACtB,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IAAM,iBAAiB,OAAO,EAAE;oEACzC,WAAU;;sFAEV,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BArIzC,GAAG,OAAO,EAAE,EAAE;;;;;oBAgJzB,KAEA,MAAM,OAAO,EAAE,IAAI,CAAC;wBAClB,6EAA6E;wBAC7E,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;wBAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;wBAEJ,qBACE,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCACC,WAAU;oCACV,SAAS,IAAM,kBAAkB,OAAO,MAAM;;sDAE9C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDACV,KACE,YAAY,UAAU,qBAAqB;oDAE7C,WAAU;;;;;;8DAEZ,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DACV,YAAY,UAAU,YAAY;;;;;;8DAErC,8OAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,WACb,gBACA,OAAO,IAAI,KAAK,cACd,aACA;;;;;;gDAGP,OAAO,MAAM,KAAK,aAAa,oBAC9B,8OAAC;oDAAE,WAAU;8DACV,OAAO,OAAO,IAAI,cAAc,OAAO,OAAO,GAC3C,CAAC,SAAS,EAAE,AAAC,OAAO,OAAO,CAAqC,QAAQ,EAAE,GAC1E,YAAY,CAAC,OAAO,MAAM,CAAC,EAAE,UAAU,WACrC,CAAC,SAAS,EAAE,YAAY,CAAC,OAAO,MAAM,CAAC,EAAE,UAAU,UAAU,GAC7D;;;;;;;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;wCAEZ,OAAO,MAAM,KAAK,aAAa,MAC9B,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,gCAC/B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,QAAQ;4CACR,OAAM;sDAEN,cAAA,8OAAC,kMAAA,CAAA,OAAQ;gDAAC,WAAU;;;;;;;;;;;wCAKzB,OAAO,MAAM,KAAK,aAAa,oBAC9B,8OAAC,4IAAA,CAAA,eAAY;4CACX,MAAM,yBAAyB,OAAO,MAAM;4CAC5C,cAAc,CAAC;gDACb,IAAI,MAAM;oDACR,wBAAwB,OAAO,MAAM;gDACvC,OAAO,IAAI,yBAAyB,OAAO,MAAM,EAAE;oDACjD,wBAAwB;gDAC1B;4CACF;;8DAEA,8OAAC,4IAAA,CAAA,sBAAmB;oDAAC,OAAO;8DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;kEAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG9B,8OAAC,4IAAA,CAAA,sBAAmB;oDAClB,OAAM;oDACN,iBAAiB,IACf,wBAAwB;;wDAIzB,aAAa,CAAC,OAAO,MAAM,CAAC,KAAK,wBAChC,8OAAC,4IAAA,CAAA,mBAAgB;4DACf,SAAS,IACP,wBAAwB,OAAO,MAAM;4DAEvC,UAAU,gBAAgB,CAAC,OAAO,MAAM,CAAC;sEAExC,gBAAgB,CAAC,OAAO,MAAM,CAAC,iBAC9B;;kFACE,8OAAC;wEAAI,WAAU;;;;;;oEAA6F;;6FAI9G;;kFACE,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAA+B;;;;;;;;wDAQ1D,CAAC,oBAAoB,YACnB,oBAAoB,eACnB,OAAO,IAAI,KAAK,QAAS,mBAC3B;;gEACG,oBAAoB,YACnB,OAAO,IAAI,KAAK,0BACd,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,oBAAoB,OAAO,MAAM;;sFAGnC,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAIxC,oBAAoB,YACnB,OAAO,IAAI,KAAK,6BACd,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,mBAAmB,OAAO,MAAM;;sFAGlC,8OAAC,gNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;gEAI3C,oBAAoB,YACnB,CAAC,OAAO,IAAI,KAAK,YACf,OAAO,IAAI,KAAK,WAAW,mBAC3B,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,sBAAsB,OAAO,MAAM;oEAErC,WAAU;;sFAEV,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAIxC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8EACtB,8OAAC,4IAAA,CAAA,mBAAgB;oEACf,SAAS,IACP,iBAAiB,OAAO,MAAM;oEAEhC,WAAU;;sFAEV,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAnJzC,GAAG,OAAO,MAAM,EAAE;;;;;oBA8J7B;;;;;;gBAGL,qBAAqB,gCACpB,8OAAC,8IAAA,CAAA,UAAa;oBACZ,MAAM;oBACN,QAAQ;oBACR,cAAc,CAAC;wBACb,qBAAqB;wBACrB,IAAI,CAAC,MAAM;4BACT,kBAAkB;4BAClB,yBAAyB;wBAC3B;oBACF;oBACA,cAAc,eAAe,EAAE,KAAK,aAAa;oBACjD,8BAA8B;;;;;;8BAKlC,8OAAC,2IAAA,CAAA,cAAW;oBACV,MAAM;oBACN,cAAc;8BAEd,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;0CACjB,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,8OAAC,2IAAA,CAAA,yBAAsB;kDAAC;;;;;;;;;;;;0CAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,oBAAiB;wCAAC,UAAU;kDAAc;;;;;;kDAC3C,8OAAC,2IAAA,CAAA,oBAAiB;wCAChB,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAA0F;;2DAI3G;;;;;;;;;;;;;;;;;;;;;;;8BAQV,8OAAC,2IAAA,CAAA,cAAW;oBAAC,MAAM;oBAAkB,cAAc;8BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;0CACjB,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,8OAAC,2IAAA,CAAA,yBAAsB;kDAAC;;;;;;;;;;;;0CAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,oBAAiB;wCAAC,UAAU;kDAAc;;;;;;kDAC3C,8OAAC,2IAAA,CAAA,oBAAiB;wCAChB,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAA0F;;2DAI3G;;;;;;;;;;;;;;;;;;;;;;;8BAQV,8OAAC,2IAAA,CAAA,cAAW;oBAAC,MAAM;oBAAgB,cAAc;8BAC/C,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;0CACjB,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,mBAAgB;kDAAC;;;;;;kDAClB,8OAAC,2IAAA,CAAA,yBAAsB;kDAAC;;;;;;;;;;;;0CAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;kDAChB,8OAAC,2IAAA,CAAA,oBAAiB;wCAAC,UAAU;kDAAc;;;;;;kDAC3C,8OAAC,2IAAA,CAAA,oBAAiB;wCAChB,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,6BACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAA0F;;2DAI3G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQhB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,8BAA8B,EAAE,CAAC,YAAY,aAAa,IAAI;;YAGzE,uBACC,8OAAC,qJAAA,CAAA,UAAsB;gBACrB,SAAS,MAAM,EAAE;gBACjB,gBAAgB;;;;;;0BAGpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAW,GAAG,YAAY,kCAAkC,gBAAgB;gCAC5E,SAAS;0CAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,mBAAmB;;sDAElC,8OAAC,kIAAA,CAAA,cAAW;4CACV,KAAK,MAAM,SAAS,IAAI;4CACxB,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,MAAM,IAAI,EAAE,MAAM,GAAG,GAAG,iBAAiB;;;;;;;;;;;;8CAG9C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyB,MAAM,IAAI;;;;;;wCAChD,oBAAoB,0BACnB,8OAAC;4CACC,WAAU;4CACV,SAAS,IAAM,sBAAsB;sDAErC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,uBAAuB;oDACzB;8DAEA,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC,uIAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAA6B;wDACzC;wDAAY;;;;;;;;;;;;;sDAGlB,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,8OAAC,uIAAA,CAAA,qBAAkB;8CACjB,cAAA,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,mBAAmB;;0DAElC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;;oDAAW;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC,uIAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,8OAAC,uIAAA,CAAA,qBAAkB;8CACjB,cAAA,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,qBAAqB;;0DAEpC,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC,uIAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,8OAAC,uIAAA,CAAA,qBAAkB;8CAChB,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,WAAW,MAAM,GAAG,kBACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAClC,8OAAC;wDAEC,WAAU;wDACV,SAAS;4DACP,sBAAsB;4DACtB,mBAAmB;wDACrB;wDACA,OAAO,MAAM,QAAQ,IAAI;kEAExB,MAAM,QAAQ,EAAE,WAAW,MAAM,oBAClC,MAAM,IAAI,KAAK,wBACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,WAAU;oEACV,KAAK,MAAM,GAAG;oEACd,KAAK;oEACL,SAAQ;;;;;;8EAEV,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;iFAIrB,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;4DAAC;;;;;;uDAxB7C;;;;;;;;;;0DA8BX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAML,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAS7C,8OAAC,uIAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,8OAAC,uIAAA,CAAA,qBAAkB;8CAChB,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,UAAU,MAAM,GAAG,kBACrB,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC,IAAI,GAAG,EAAE;oDACpC,OAAO,IAAI,QAAQ;;sEAEnB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;sEAEvB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFACV,IAAI,QAAQ;;;;;;kFAEf,8OAAC;wEAAE,WAAU;kFACV,IAAI,QAAQ,EAAE,iBACb,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,GAAG,CAAC;;;;;;;;;;;;;;;;;sEAI5D,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;sEAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;mDAxBrB;;;;;4CA4BR,UAAU,MAAM,GAAG,mBAClB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAOP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC,uIAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,8OAAC,uIAAA,CAAA,qBAAkB;8CAChB,+BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,MAAM,MAAM,GAAG,kBACjB,8OAAC;wCAAI,WAAU;;4CACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM;gDAC5B,0BAA0B;gDAC1B,MAAM,SAAS,KAAK,GAAG,CACpB,OAAO,CAAC,gBAAgB,IACxB,KAAK,CAAC,IAAI,CAAC,EAAE;gDAChB,uBAAuB;gDACvB,MAAM,OAAO,IAAI,KAAK,KAAK,SAAS;gDACpC,MAAM,gBAAgB,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;gDAE1H,qBACE,8OAAC;oDAEC,WAAU;oDACV,SAAS,IACP,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE,UAAU;oDAElC,OAAO,KAAK,KAAK;;sEAEjB,8OAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE;;;;;;sEAEf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFACV,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD,EACV,QACA,KAAK,KAAK,CAAC,MAAM,GAAG,KAChB,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,QAC9B,KAAK,KAAK;;;;;;sFAGlB,8OAAC;4EAAE,WAAU;sFACV;;;;;;;;;;;;8EAGL,8OAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;;;mDAzBA;;;;;4CA8BX;4CACC,MAAM,MAAM,GAAG,mBACd,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAOP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;;sDAE1B,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;gCAIP,oBAAoB,YAAY,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,mBACvE,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,gCAAgC;;sDAE/C,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;gCAKT,oBAAoB,0BACnB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,oBAAoB;;sDAEnC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;gCAKT,CAAC,CAAC,oBAAoB,YAAY,MAAM,OAAO,EAAE,WAAW,CAAC,mBAC5D,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;wCACP,+DAA+D;wCAC/D,IAAI,oBAAoB,UAAU;4CAChC,gCAAgC;wCAClC,OAAO;4CACL,mBAAmB;wCACrB;oCACF;;sDAEA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOf,qBAAqB,gCACpB,8OAAC,8IAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,cAAc,eAAe,EAAE,KAAK,aAAa;;;;;;0BAKrD,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAiB,cAAc;0BAChD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAM1B,8OAAC;4BAAI,WAAU;sCACZ,MAAM,OAAO,EACV,OAAO,CAAC,SAAW,OAAO,MAAM,KAAK,aAAa,IAAI,6BAA6B;6BACpF,IAAI,CAAC;gCACJ,MAAM,aAAa,aAAa,CAAC,OAAO,MAAM,CAAC;gCAC/C,MAAM,WAAW,YAAY,UAAU,WACnC,WAAW,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW,KACpD;gCAEJ,qBACE,8OAAC;oCAEC,WAAU;oCACV,SAAS,IAAM,sBAAsB,OAAO,MAAM;;sDAElD,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDACV,KACE,YAAY,UAAU,qBAAqB;oDAE7C,WAAU;;;;;;8DAEZ,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DACV,YAAY,UAAU,YAAY;;;;;;8DAErC,8OAAC;oDAAE,WAAU;8DACV,OAAO,IAAI,KAAK,cACb,aACA;;;;;;;;;;;;;mCAtBH,CAAC,SAAS,EAAE,OAAO,MAAM,EAAE;;;;;4BA2BtC;;;;;;sCAEJ,8OAAC,2IAAA,CAAA,oBAAiB;sCAChB,cAAA,8OAAC,2IAAA,CAAA,oBAAiB;gCAAC,UAAU;0CAAc;;;;;;;;;;;;;;;;;;;;;;0BAMjD,8OAAC,2IAAA,CAAA,cAAW;gBACV,MAAM;gBACN,cAAc;0BAEd,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAGlB,8OAAC,2IAAA,CAAA,yBAAsB;8CACpB,eAAe,aAAa,CAAC,YAAY,iBACxC;;4CAAE;4CACmD;0DACnD,8OAAC;0DACE,aAAa,CAAC,YAAY,EAAE,UAAU,YACrC;;;;;;4CACK;0DAET,8OAAC;;;;;4CAAK;;uDAKR;;;;;;;;;;;;sCAIN,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,UAAU;oCACV,SAAS;wCACP,6BAA6B;wCAC7B,eAAe;oCACjB;8CACD;;;;;;8CAGD,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;YAMT,uBAAuB,uBACtB,8OAAC,8IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,cAAc;gBACd,SAAS,MAAM,EAAE;;;;;;YAKpB,mBAAmB,uBAClB,8OAAC,0IAAA,CAAA,UAAW;gBACV,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,YAAY;;;;;;YAKf,uBACC,8OAAC,kJAAA,CAAA,UAAmB;gBAClB,OAAO;gBACP,QAAQ;gBACR,cAAc;gBACd,QAAQ,IAAM,sBAAsB;gBACpC,WAAW,CAAC;oBACV,gCAAgC;oBAChC,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;oBACvC,IAAI,UAAU,aAAa,EAAE,OAAO,aAAa,EAAE,EAAE;wBACnD,UAAU,gBAAgB,CAAC;oBAC7B;oBAEA,4EAA4E;oBAC5E,WAAW;wBACT,OAAO,QAAQ,CAAC,MAAM;oBACxB,GAAG;gBACL;;;;;;YAKH,uBACC,8OAAC,uIAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,SAAS,MAAM,EAAE;gBACjB,WAAW,MAAM,IAAI;;;;;;YAKxB,mBAAmB,WAAW,MAAM,GAAG,mBACtC,8OAAC,0IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,OAAO,WAAW,GAAG,CAAC,CAAC,QAAU,CAAC;wBAChC,GAAG,KAAK;wBACR,0CAA0C;wBAC1C,MACE,MAAM,QAAQ,EAAE,WAAW,MAAM,oBACjC,MAAM,IAAI,KAAK,UACX,UACA;oBACR,CAAC;gBACD,cAAc;gBACd,UAAU,MAAM,IAAI,IAAI;;;;;;0BAK5B,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAgB,cAAc;0BAC/C,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAc;;;;;;8CAC3C,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAA0F;;uDAI3G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASd,8CAA8C;IAC9C,eAAe,oBAAoB,QAAgB;QACjD,oBAAoB;QACpB,qBAAqB;QACrB,wBAAwB,OAAO,8BAA8B;IAC/D;IAEA,qCAAqC;IACrC,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB;QACrC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,kBACA,oHAAA,CAAA,YAAS,CAAC,SAAS;YAErB,IAAI,OAAO,OAAO,EAAE;gBAClB,uCAAuC;gBACvC,qBAAqB;gBAErB,mDAAmD;gBACnD,IAAI,MAAM,OAAO,EAAE;oBACjB,MAAM,iBAAiB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;wBACxC,IAAI,OAAO,MAAM,KAAK,kBAAkB;4BACtC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,oHAAA,CAAA,YAAS,CAAC,SAAS;4BAAC;wBAChD;wBACA,OAAO;oBACT;oBACA,MAAM,OAAO,GAAG;gBAClB;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,gEAAgE;gBAChE,WAAW;oBACT,kBAAkB;gBACpB,GAAG;gBAEH,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,oDAAoD;IACpD,eAAe,mBAAmB,QAAgB;QAChD,oBAAoB;QACpB,oBAAoB;QACpB,wBAAwB,OAAO,8BAA8B;IAC/D;IAEA,kCAAkC;IAClC,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB;QACrC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,kBACA,oHAAA,CAAA,YAAS,CAAC,MAAM;YAElB,IAAI,OAAO,OAAO,EAAE;gBAClB,uCAAuC;gBACvC,oBAAoB;gBAEpB,mDAAmD;gBACnD,IAAI,MAAM,OAAO,EAAE;oBACjB,MAAM,iBAAiB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;wBACxC,IAAI,OAAO,MAAM,KAAK,kBAAkB;4BACtC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,oHAAA,CAAA,YAAS,CAAC,MAAM;4BAAC;wBAC7C;wBACA,OAAO;oBACT;oBACA,MAAM,OAAO,GAAG;gBAClB;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,gEAAgE;gBAChE,WAAW;oBACT,kBAAkB;gBACpB,GAAG;gBAEH,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qCAAqC;IACrC,eAAe,iBAAiB,QAAgB;QAC9C,oBAAoB;QACpB,kBAAkB;QAClB,wBAAwB,OAAO,8BAA8B;IAC7D,yDAAyD;IACzD,kEAAkE;IACpE;IAEA,+BAA+B;IAC/B,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,kBAAkB;QACrC,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,OAAO,OAAO,EAAE;gBAClB,uCAAuC;gBACvC,kBAAkB;gBAElB,gEAAgE;gBAChE,IAAI,MAAM,OAAO,EAAE;oBACjB,MAAM,iBAAiB,MAAM,OAAO,CAAC,MAAM,CACzC,CAAC,SAAW,OAAO,MAAM,KAAK;oBAEhC,MAAM,OAAO,GAAG;gBAClB;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBAAqB;IACrB,eAAe;QACb,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE;YACzC,IAAI,OAAO,OAAO,EAAE;gBAClB,4CAA4C;gBAC5C,oBAAoB;gBAEpB,yBAAyB;gBACzB,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;gBAEvC,yBAAyB;gBACzB,UAAU,cAAc,CAAC,SAAS,MAAM,EAAE;gBAE1C,yCAAyC;gBACzC,UAAU,gBAAgB,CAAC;gBAE3B,0CAA0C;gBAC1C,MAAM,qBAAqB,mIAAA,CAAA,wBAAqB,CAAC,QAAQ;gBACzD,mBAAmB,kBAAkB,CAAC,MAAM,EAAE;gBAE9C,6BAA6B;gBAC7B;gBAEA,2BAA2B;gBAC3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,4DAA4D;IAC5D,SAAS,sBAAsB,QAAgB;QAC7C,eAAe;QACf,6BAA6B;IAC/B;IAEA,qCAAqC;IACrC,eAAe;QACb,IAAI,CAAC,OAAO,MAAM,CAAC,aAAa;QAChC,gBAAgB;QAChB,IAAI;YACF,oDAAoD;YACpD,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAClC,MAAM,EAAE,EACR,aACA,oHAAA,CAAA,YAAS,CAAC,MAAM;YAGlB,IAAI,OAAO,OAAO,EAAE;gBAClB,kBAAkB;gBAClB,6BAA6B;gBAC7B,gCAAgC;gBAEhC,mDAAmD;gBACnD,IAAI,MAAM,OAAO,IAAI,aAAa;oBAChC,MAAM,iBAAiB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;wBACxC,IAAI,OAAO,MAAM,KAAK,aAAa;4BACjC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,oHAAA,CAAA,YAAS,CAAC,MAAM;4BAAC;wBAC7C;wBACA,IAAI,OAAO,MAAM,KAAK,YAAY,EAAE,EAAE;4BACpC,OAAO;gCAAE,GAAG,MAAM;gCAAE,MAAM,oHAAA,CAAA,YAAS,CAAC,MAAM;4BAAC;wBAC7C;wBACA,OAAO;oBACT;oBACA,MAAM,OAAO,GAAG;oBAEhB,2CAA2C;oBAC3C,mBAAmB,oHAAA,CAAA,YAAS,CAAC,MAAM;gBACrC;gBAEA,8CAA8C;gBAC9C,IAAI,MAAM,OAAO,EAAE;oBACjB,SAAS;wBAAE,GAAG,KAAK;wBAAE,SAAS;+BAAI,MAAM,OAAO;yBAAC;oBAAC;gBACnD;gBAEA,gEAAgE;gBAChE,WAAW;oBACT,kBAAkB;gBACpB,GAAG;gBAEH,2BAA2B;gBAC3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,oBAAoB;gBACpB,eAAe;YACjB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBAAqB;IACrB,eAAe;QACb,IAAI,CAAC,OAAO,IAAI;QAChB,gBAAgB;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE,MAAM,EAAE;YACxC,IAAI,OAAO,OAAO,EAAE;gBAClB,uBAAuB;gBACvB,mBAAmB;gBAEnB,yBAAyB;gBACzB,MAAM,YAAY,0HAAA,CAAA,eAAY,CAAC,QAAQ;gBAEvC,yBAAyB;gBACzB,UAAU,cAAc,CAAC,SAAS,MAAM,EAAE;gBAE1C,yCAAyC;gBACzC,UAAU,gBAAgB,CAAC;gBAE3B,0CAA0C;gBAC1C,MAAM,qBAAqB,mIAAA,CAAA,wBAAqB,CAAC,QAAQ;gBACzD,mBAAmB,kBAAkB,CAAC,MAAM,EAAE;gBAE9C,6BAA6B;gBAC7B;gBAEA,2BAA2B;gBAC3B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,KAAK,EAAE;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;AACF", "debugId": null}}]}